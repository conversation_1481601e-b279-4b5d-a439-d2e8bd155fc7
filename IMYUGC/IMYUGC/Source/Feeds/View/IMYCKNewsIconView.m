//
//  IMYCKNewsIconView.m
//  IMYCommonKit
//
//  Created by 林云峰 on 2021/3/16.
//

#import "IMYCKNewsIconView.h"
#import <IMYBaseKit/IMYViewKit.h>

@implementation IMYCKNewsIconModel

+ (IMYCKNewsIconModel *)answerIcon {
    IMYCKNewsIconModel *icon = [IMYCKNewsIconModel new];
    icon.text = IMYString(@"问");
    icon.size = CGSizeMake(16, 16);
    icon.textColor = [UIColor whiteColor];
    icon.bgColor = [UIColor colorWithRed:255/255.f  green:77/255.f blue:136/255.f alpha:1];
    return icon;
}
+ (IMYCKNewsIconModel *)voteIcon {
    IMYCKNewsIconModel *icon = [IMYCKNewsIconModel new];
    icon.text = IMYString(@"投");
    icon.size = CGSizeMake(16, 16);
    icon.textColor = [UIColor whiteColor];
    icon.bgColor = [UIColor colorWithRed:71/255.f  green:148/255.f blue:255/255.f alpha:1];
    return icon;

}

+ (IMYCKNewsIconModel *)iconWithTitle:(NSString *)title color:(NSString *)color {
    if (!title || !color) {
        return nil;
    }
    IMYCKNewsIconModel *icon = [IMYCKNewsIconModel new];
    icon.text = title;
    CGFloat width = ceilf([title boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 16) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:11]} context:nil].size.width);
    if (title.length == 1) {
        icon.size = CGSizeMake(16, 16);
    } else {
        icon.size = CGSizeMake(MAX(16, width + 8), 16);
    }
    
    UIColor *colorValue = [UIColor imy_colorWithHexString:color];
    icon.textColor = [UIColor whiteColor];
    icon.bgColor = [colorValue colorWithAlphaComponent:1];
    return icon;

}

+ (IMYCKNewsIconModel *)iconWithTagData:(NSDictionary *)dictionary {
    if (imy_isEmptyString(dictionary[@"name"])) {
        return nil;
    }
    if (imy_isEmptyString(dictionary[@"color"])) {
        return nil;
    }
    
    IMYCKNewsIconModel *icon = [IMYCKNewsIconModel new];
    icon.text = dictionary[@"name"];
    icon.tag_id = dictionary[@"tag_id"];
    icon.tag_type = dictionary[@"tag_type"];
    icon.type = dictionary[@"type"];
    icon.fillStyle = [dictionary[@"fill_style"] intValue];
    icon.type = dictionary[@"type"];
    
    CGFloat width = ceilf([icon.text boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 16) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:11]} context:nil].size.width);
    if (icon.text.length == 1) {
        icon.size = CGSizeMake(16, 16);
    } else {
        icon.size = CGSizeMake(MAX(16, width + 5), 16);
    }
    UIColor *colorValue = [UIColor imy_colorWithHexString:dictionary[@"color"]];

    if ([@"doctor_patient_qa" isEqualToString:icon.type] || [@"doctor_patient_qa" isEqualToString:icon.tag_type]) {
        icon.textColor = colorValue;
        icon.bgColor = [UIColor imy_colorForKey:kCK_White_AN];
    }else{
        icon.textColor = [UIColor whiteColor];
        icon.bgColor = [colorValue colorWithAlphaComponent:1];
    }
        
    return icon;
}
@end

@implementation IMYCKNewsIconView

+ (UIView *)iconWithModel:(IMYCKNewsIconModel *)model {
    if (!model) {
        return nil;
    }
    IMYCKNewsIconView *tagView = [[IMYCKNewsIconView alloc] initWithFrame:CGRectMake(0, 0, model.size.width, model.size.height)];
    tagView.textAlignment = NSTextAlignmentCenter;
    tagView.font = [UIFont systemFontOfSize:11];
    if ([@"doctor_patient_qa" isEqualToString:model.type] || [@"doctor_patient_qa" isEqualToString:model.tag_type]) {
        UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:tagView.bounds cornerRadius:4];
        CAShapeLayer *borderLayer = [CAShapeLayer layer];
        borderLayer.path = path.CGPath;
        borderLayer.strokeColor = model.textColor.CGColor;
        borderLayer.fillColor = [UIColor clearColor].CGColor;
        borderLayer.frame = tagView.bounds;
        borderLayer.lineWidth = 0.5;
        [tagView.layer addSublayer:borderLayer];
    }else{
        tagView.layer.cornerRadius = 4;
        tagView.layer.masksToBounds = YES;
    }
       
    tagView.text = model.text;
    tagView.textColor = model.textColor;
    tagView.backgroundColor = model.bgColor;
    return tagView;
}

@end

