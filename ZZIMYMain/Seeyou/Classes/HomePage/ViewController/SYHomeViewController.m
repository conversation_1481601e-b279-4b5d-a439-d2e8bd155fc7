//
//  SYHomeViewController.m
//  Seeyou
//
//  Created by 施东苗 on 2020/9/22.
//  Copyright © 2020 linggan. All rights reserved.
//

#import "SYHomeViewController.h"
#import "SYBaseTabBarController.h"

#import <IMYCommonKit/IMYCommonPageEvent.h>
#import <IMYCommonKit/IMYCKRefreshHeader.h>
#import <IMYCommonKit/IMYNestTableView.h>
#import <IMYBaseKit/IMYTableViewAdapter.h>
#import <IMYRecord/IMYRecordDateSwitchBannerAdapter.h>
#import <IMYCommonKit/IMYHPVAdapter.h>
#import <IMYCommonKit/IMYModuleConfigManager.h>
#import <IMYCommonKit/IMYCKDynamicNavBarManager.h>
#import <IMYMSG/IMYMsgBannerAdapter.h>
#import <IMYBaseKit/IMYGlobalColorManager.h>
#import <IMYMSG/IMYMsg2Manager.h>

#import <IMYAdvertisement/IMYAdvertisementSDK.h>
#import <IMYRecord/IMYRecordDefines.h>
#import <IMYBaseKit/UITabBarItem+IMYTabBarItemManager.h>
#import "IMYRecordHomeToolsAdapter.h"
#import "IMYYunYuABManager.h"
#if   __has_include("IMYMixFeedsAdapter.h")
#import <IMYNews/IMYMixFeedsAdapter.h>
#import <IMYNews/IMYUGCFeedsContainerAdapter.h>
#import <IMYNews/IMYUGCHomeAdapter.h>

#endif

#import "SYQinYouChangeModeManager.h"
#import "SYYouthModeActionDialog.h"
#import "SYYouthModePopupTipCoverView.h"
#import "SYYoutModeConfigManager.h"
#import "SYUserModeFixManager.h"
#import "IMYWatchSessionManager.h"
#import "SYHomeRecordCoverView.h"
#import "SYABTestManager.h"

#import <IMYMe/IMYPrivacySettingsUtils.h>

#import <IMYCommonKit/IMYCKABTestManager.h>
#import "IMYCK2ndFRefreshHeaderV2.h"
#import "IMY2ndFloorManager.h"
#import <IMYBaseKit/IMYAlertShowAutoRobot.h>
#if __has_include(<IMYMSG/IMYMSG.h>)
#import <IMYMSG/IMYMsgNotifyManager.h>
#endif

@interface SYHomeTouchView : UIView
@property (nonatomic, copy) void(^hitBlock)(void);
@end

@implementation SYHomeTouchView

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    UIView *view = [super hitTest:point withEvent:event];
    if (self.hitBlock) {
        self.hitBlock();
    }
    return view;
}

@end

@interface SYHomeViewController() <UITableViewDelegate>
@property (nonatomic, strong) IMYCKDynamicNavBarManager *navBarManager;
@property (nonatomic, strong) IMYNestTableView *tableView;

@property (nonatomic, strong) IMYTableViewAdapter *tableViewAdapter;
/// 多分类的大社区adapter
#if   __has_include("IMYMixFeedsAdapter.h")
@property (nonatomic, strong) IMYUGCHomeAdapter *feedsAdapter;
#endif

@property (nonatomic, strong) IMYMsgBannerAdapter<IMYTableViewAdapterModuleDelegate> *msgBannerAdapter;
@property (nonatomic, strong) IMYHPVAdapter<IMYTableViewAdapterModuleDelegate> *hpvAdapter;

@property (nonatomic, strong) IMYRecordDateSwitchBannerAdapter<IMYTableViewAdapterModuleDelegate> *dateSwitchBannerAdapter;
@property (nonatomic, strong) IMYRecordHomeToolsAdapter<IMYTableViewAdapterModuleDelegate> *toolsAdapter;

@property (nonatomic, strong) NSDictionary *cardDataDict; //卡片化数据，由接口获取

//BI相关
@property (nonatomic, assign) BOOL hasTemplateIDPost;                   //模板ID是否上报过
@property (nonatomic, strong) NSNumber *realTemplateID;                 //真实模板ID
@property (nonatomic, strong) NSNumber *theoreticalTemplateID;          //理论模板ID

/// 刷新控件
@property (nonatomic, strong) IMYCK2ndFRefreshHeaderV2 *refreshHeaderV2;

//广告相关
@property (nonatomic, strong) id<IMYIAdManager> chapingAd;
@property (nonatomic, assign) BOOL  requestChaPingAd ;
@property (nonatomic, strong) id<IMYITableViewAdManager> sideAdManager;
//@property (nonatomic, strong) id<IMYIAdapterAdPage> twoFloorAdManager;
//@property (nonatomic, strong) IMYAdSignal *adSignal;
//@property (nonatomic, strong) IMYAdSignal *startAnimationSignal;
//@property (nonatomic, strong) IMYAdSignal *endShowAnimationSignal;

@property (nonatomic, strong) id<IMYITableViewAdManager> adManager; //首页广告

/// 是否有切换用户
@property (nonatomic, assign) BOOL useridChanged;

@end


@implementation SYHomeViewController

- (void)dealloc {
    NSLog(@"SYHomeViewController dealloc");
}

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    [self.tabBarItem imy_setFinishedSelectedImageName:@"all_bottommeetyou_up" withFinishedUnselectedImageName:@"all_bottommeetyou"];
    self.tabBarItem.imageInsets = UIEdgeInsetsMake(-5, 0, 5, 0);
    self.tabBarItem.titlePositionAdjustment = UIOffsetMake(0, -1.5);
    [self resetTabName];
    return self;
}

- (void)resetTabName {
    //https://www.tapd.cn/21039721/bugtrace/bugs/view/1121039721001127315
    //https://www.tapd.cn/21039721/bugtrace/bugs/view/1121039721001126958
    //以下两个设置title都需要
    NSString *title = IMYString(@"美柚");
#if   __has_include("IMYMixFeedsAdapter.h")
    if (self.feedsAdapter.channelStyle == IMYUGCChannelStyleNone && [IMYPublicAppHelper shareAppHelper].isPersonalRecommand) {
        title = IMYString(@"推荐");
    }
#endif

    self.navBarManager.customerNormalTitle = title;
    if (self.isViewActived && self.navBarManager.downBarIconStyle) {
        title = IMYString(@"去推荐");
    }
    SYBaseTabBarController *tabVC = [SYBaseTabBarController shareTabbarController];
    if (tabVC.isViewDidAppear) {
        // 当首页未被选中时，固定展示美柚
        if (tabVC.selectedIndex != 0) {
            title = IMYString(@"美柚");
        }
        tabVC.tabBar.items.firstObject.title = title;
        self.tabBarItem.title = title;
    } else {
        self.tabBarItem.title = title;
    }
}

- (BOOL)isNavigationBarHidden {
    return YES;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [[IMYCommonPageEvent shareManager] pageEventDidAppear];
#if   __has_include("IMYMixFeedsAdapter.h")

    [self.feedsAdapter.adapter viewDidAppear];
#endif
    // 判断是否需要展示右上角青少年新功能提示气泡弹窗
    [self showYouthModeTopTipBubbleIfNeeded:2.0f];
    // 延迟3秒判断是否需要展示首页底部青少年弹窗
    [self showYouthModeBottomDialogIfNeeded:3.0f];
    
    // 二楼数据（首次请求）
    [self reloadUdimDatasIfNeed];
}

- (void)loadView {
    SYHomeTouchView *view = [[SYHomeTouchView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - SCREEN_TABBAR_HEIGHT - SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
    [view setHitBlock:^{
        
    }];
    self.view = view;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    _requestChaPingAd = YES;
    [self loadDefaultCardData];
    
    //UI
    self.navBarManager = [IMYCKDynamicNavBarManager new];
#if   __has_include("IMYMixFeedsAdapter.h")
    self.navBarManager.downBarIconStyle = [IMYUGCHomeAdapter tabbarIconDownStyle];
#endif
    self.navBarManager.bindingVC = self;
    self.navBarManager.customerNormalTitle = IMYString(@"美柚");
    [self updateNavBarConfig];
    [self.view addSubview:self.navBarManager.navBar];
    [IMYGlobalColorManager registView:self.navBarManager.navBar style:IMYGlobalColorStyleHomeFirstVisible];
    [IMYGlobalColorManager registView:self.navBarManager.navBar style:IMYGlobalColorStyleHomeHalf];
    
    self.tableView = [[IMYNestTableView alloc] initWithFrame:self.view.bounds];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [self.tableView imy_setBackgroundColorForKey:kIMY_BG];
    self.tableView.estimatedRowHeight = 0;
    self.tableView.estimatedSectionHeaderHeight = 0;
    self.tableView.estimatedSectionFooterHeight = 0;
    self.tableView.supportNoCeiling = YES;
    self.tableView.imy_top = SCREEN_NAVIGATIONBAR_HEIGHT;
    self.tableView.imy_height = SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - SCREEN_TABBAR_HEIGHT;
    [self.view addSubview:self.tableView];
    [self addTableViewRefreshHeaderV2];
    [IMYGlobalColorManager registView:self.tableView style:IMYGlobalColorStyleHomeFirstVisible];
    
    //Adapter
    self.tableViewAdapter = [IMYTableViewAdapter adpaterWithTableView:self.tableView];
    self.tableViewAdapter.UIDelegate = self;
    
    //dateSwitchBannerAdapter,msgBannerAdapter同一个key(jq_top_banner)
    [self.tableViewAdapter registerModuleDelegate:self.dateSwitchBannerAdapter];
    self.msgBannerAdapter = IMYMsgBannerAdapter.new;
    [self.tableViewAdapter registerModuleDelegate:self.msgBannerAdapter];
    
    self.toolsAdapter = IMYRecordHomeToolsAdapter.new;
    [self.tableViewAdapter registerModuleDelegate:self.toolsAdapter];
    
    self.hpvAdapter = IMYHPVAdapter.new;
    [self.tableViewAdapter registerModuleDelegate:self.hpvAdapter];
    
    // [self initTwoFloorAd];
    [self initSideAD:[self useTabFeeds]];
    [self initHomeAd:[self useTabFeeds]];
    @weakify(self);
#if   __has_include("IMYMixFeedsAdapter.h")
    self.feedsAdapter = [IMYUGCHomeAdapter adapterWithChannel:[self useTabFeeds] inTableView:self.tableView parentVC:self entrance:1 adUserInfo:[self mixFeedsAdUserInfo] headerAdManager:((id<IMYIAdapterAdManager>)self.adManager).pageAdapters];
    [self.feedsAdapter loadExperiment];
    [self.tableViewAdapter registerModuleDelegate:self.feedsAdapter.adapter];

    [self.feedsAdapter.adapter.requestFinishedSignal subscribeNext:^(NSNumber *x) {
        @strongify(self);
        [self.tableViewAdapter reloadModules];
    }];
    [self.feedsAdapter.adapter setShowTabbarArrowBlock:^(BOOL show) {
        @strongify(self);
        self.navBarManager.barType = show?IMYCKBarTypeCeiling:IMYCKBarTypeNormal;
    }];
    
    if ([self.feedsAdapter.adManager conformsToProtocol:@protocol(IMYIAdapterAdPage)]) {
        [((id<IMYIAdapterAdManager>)self.adManager) registerAdPageAdapter:(id<IMYIAdapterAdPage>)_feedsAdapter.adManager];
    }
    if ([self.feedsAdapter getAdUserInfo]) {
        [self.adManager.adInfo unlock];
        [self.adManager.adInfo appendUserInfo:[self.feedsAdapter getAdUserInfo]];
        [self.adManager.adInfo lock];
    }
    
    /// 是频道方式大社区的话会回调
    [self.feedsAdapter setChannelTableViewChangeBlock:^(UITableView * _Nonnull tableView) {
        @strongify(self);
        [self updateSideAdSubView:tableView];
    }];
#endif
    // 初始化新知识埋点View
    [self setupKnowBiView];
    [self.tableViewAdapter refreshModulesWithData:self.cardDataDict completedBlock:^{
        @strongify(self);
        [self.tableViewAdapter reloadModules];
    }];
     
    [self resetTabName];
    // 添加各种监听
    [self addNotification];
     
    //取默认数据，刷新
    [self loadDefaultCardData];
    /// 请求数据
    [self refreshData];
}

- (void)loadDefaultCardData {
    //取默认数据，刷新
    NSDictionary *dataDict = [[NSUserDefaults standardUserDefaults] objectForKey:@"CardHomeDefaultDataKey"];
    if (!dataDict) {
        dataDict = [[IMYCacheHelper sharedCacheManager] objectForKey:@"CardHomeDefaultDataKey"];
    } else {
        // 升级上来迁移老的存储对象
        [[IMYCacheHelper sharedCacheManager] setObject:dataDict forKey:@"CardHomeDefaultDataKey"];
        [[IMYUserDefaults standardUserDefaults] removeObjectForKey:@"CardHomeDefaultDataKey"];
    }
    if (!dataDict || dataDict.count == 0) {
        NSURL *url = [[NSBundle mainBundle] URLForResource:@"CardHomeDefaultData" withExtension:@"json"];
        NSData *defaultData = [[NSData alloc] initWithContentsOfURL:url];
        NSDictionary *dict = [defaultData imy_jsonObject];
        dataDict = [dict objectForKey:@"data"];
    }
    self.cardDataDict = dataDict;
    self.realTemplateID = [self.cardDataDict.allValues.firstObject valueForKey:@"template_id"] ?: @(0);
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self initChaPingAdmanager:YES];
#if   __has_include("IMYMixFeedsAdapter.h")
    [self.feedsAdapter.adapter viewWillAppear];
#endif
}

- (void)viewWillDisappear:(BOOL)animated {
    [self.navBarManager tabStopRefresh];
    [super viewWillDisappear:animated];
#if   __has_include("IMYMixFeedsAdapter.h")
    [self.feedsAdapter.adapter viewWillDisappear];
#endif
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self.dateSwitchBannerAdapter hiddenWhenDisappear];
    [self.dateSwitchBannerAdapter hiddenBriefReportWhenDisappear];
}

- (void)addNotification {
    @weakify(self);
    
    //tab名称刷新
    RACSignal *personalRecommandChangeSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:@"IMYPersonalRecommandStateChanged" object:nil];
    RACSignal *youngModeChangeSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:@"IMYYouthModeStateChanged" object:nil];

    [[[RACSignal merge:@[[IMYPublicAppHelper shareAppHelper].useridChangedSignal, personalRecommandChangeSignal, youngModeChangeSignal]] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
        imy_asyncMainBlock(^{
            @strongify(self);
            [self resetTabName];
        });
    }];
    
    //tab旋转结束
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"kIMYNewsRootContainerRefreshKey" object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *object) {
        [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:@"home/showBadgesCount" params:@{@"badgesCount": @(0)} info:nil]];
    }];
        
    // 点Tab
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"REFRESH_SY" object:nil] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        //tab旋转
        if (self.navBarManager.downBarIconStyle && self.navBarManager.barType == IMYCKBarTypeNormal) {
            [self scrollToFeeds];
            return;
        }
        /// 发通知给大社区模块更新。
        IMY_POST_NOTIFY(@"Feeds_Refresh");
        [self.navBarManager tabStartRefresh];
        self.tableView.scrollEnabled = YES;
        self.tableView.canScrolling = YES;
        [self.tableView scrollToTopAction];
        [self.tableView imy_headerBeginRefreshing];
#if   __has_include("IMYMixFeedsAdapter.h")
        self.feedsAdapter.adapter.bi_refreshType = 2;
#endif
    }];
    
    //重新刷新页面
    //开关值变更
    RACSignal *configChangeSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:IMYModuleConfigChangeNotification object:nil];
    //用户年龄变更
    RACSignal *userBirthdayChangedSignal = [IMYPublicAppHelper shareAppHelper].userBirthdayChangedSignal;
    //用户ID变更
    RACSignal *useridChangedSignal = [IMYPublicAppHelper shareAppHelper].useridChangedSignal;
    //日历刷新
    RACSignal *recordReloadSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:kNotificationRecordCalendarReload object:nil];
    [[[[[[RACSignal merge:@[configChangeSignal, userBirthdayChangedSignal, useridChangedSignal, recordReloadSignal]] takeUntil:self.rac_willDeallocSignal] throttle:0.5] skip:0] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        /// 需要刷新数据
        BOOL isCalendarReload = NO;
        if ([x isKindOfClass:NSNotification.class] ) {
            /// 日历数据刷新不用更新资讯
            NSNotification *notification = (NSNotification *)x;
            if ([notification.name isEqualToString:@"syReshowCalendarView"]) {
                isCalendarReload = YES;
            }
        }
        [self.tableViewAdapter refreshModulesWithData:self.cardDataDict completedBlock:^{
            @strongify(self);
            [self.tableViewAdapter reloadModules];
            if (!isCalendarReload) {
                /// 请求大社区流知识接口,
#if   __has_include("IMYMixFeedsAdapter.h")
                [self.feedsAdapter.adapter requestData];
#endif

                // 刷新广告接口
//                [self.adAdapter requestData];
            }
        }];
    }];
    
    [[[[useridChangedSignal skip:1] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self updateNavBarConfig];
        [self.tableView setContentOffset:CGPointZero];
        self.useridChanged = YES;
    }];
    
    [[[[[[IMYRightsSDK sharedInstance].loadedSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] throttle:0.5] skip:1] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        if (self.useridChanged) {
            self.useridChanged = NO;
            
            //卡片化内容
            [self refreshData];
    #if   __has_include("IMYMixFeedsAdapter.h")
            if (self.feedsAdapter.adapter.bi_refreshType != 2) {
                self.feedsAdapter.adapter.bi_refreshType = 3;
            }
            [self.feedsAdapter.adapter requestData];
    #endif
        }
    }];
    
    [[[[IMYConfigsCenter sharedInstance].loadedSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self updateNavBarConfig];
        
        [self handleConfigloadedFor2ndFloor];
    }];
    
    [[[[IMYPublicAppHelper shareAppHelper].youngModeChangedSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self updateNavBarConfig];
    }];
    
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"IMYEBDynamicHomeVC.Refresh.Notification" object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *note) {
        @strongify(self);
        [self updateNavBarConfig];
    }];
    
#if __has_include(<IMYMSG/IMYMSG.h>)
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:K_MsgActionBoxShowing_Change object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *object) {
        @strongify(self);
        [self showGuideView];
    }];
#endif
    [[[[IMYABTestManager sharedInstance].loadedSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        /// 注册弹窗
        [self registerAlertRobot];
    }];
}

//- (void)addTableViewRefreshHeader {
//    // 是否是用 v2
//    if (self.useRefreshHeaderV2) {
//        [self addTableViewRefreshHeaderV2];
//        return;
//    }
//    
//    if (self.refreshHeader) {
//        self.tableView.mj_header = self.refreshHeader;
//        return;
//    }
//    
//    @weakify(self);
//    self.refreshHeader = [IMYCKRefreshHeader headerWithRefreshingBlock:^{
//        @strongify(self);
//        //卡片化内容
//        [self refreshData];
//#if   __has_include("IMYMixFeedsAdapter.h")
//        if (self.feedsAdapter.adapter.bi_refreshType != 2) {
//            self.feedsAdapter.adapter.bi_refreshType = 3;
//        }
//        [self.feedsAdapter.adapter requestData];
//#endif
//        //会员免广告toast
//        imy_asyncMainBlock(0.7, ^{
//            [[IMYURIManager sharedInstance] runActionWithString:@"tips/advertisingFreeMembership"];
//        });
//
//        //bi report
//        NSDictionary *params = @{@"event":@"yy_sy_xlsx",
//                                 @"action":@(1),
//                                 @"public_type":@"妈妈页"};
//        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
//    }];
//    self.refreshHeader.isUserModeType = YES;
//    self.tableView.mj_header = self.refreshHeader;
//}

- (void)updateNavBarConfig {
    if ([IMYPublicAppHelper shareAppHelper].useYoungMode) {
        [self.navBarManager changeRight2Young];
    } else if ([IMYYunYuABManager isNavSearchStyleNotbaby]) {
        [self.navBarManager changeRight2Publish];
    } else {
        [self.navBarManager changeRight2SignV2];
    }
    
    if ([IMYMsg2Manager isShowMsgTab]) {
        [self.navBarManager changeLeft2Type:IMYCKBarLeftType_Search];
        [self.navBarManager changeTitle2Type:IMYCKBarTitleType_Title];
    } else {
        [self.navBarManager changeLeft2Type:IMYCKBarLeftType_Default];
        [self.navBarManager changeTitle2Type:IMYCKBarTitleType_Default];
    }
}

#pragma mark - Card Handle

- (void)refreshData {
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeNormal) {
        // 卡片数据
        [self requestCardData];
    }
    //请求广告数据
    //修复：未登录-新安装app，首次启动-》选择备孕身份，经期首页广告会发起广告请求（异常）
    if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeNormal) {
        [self requestAdData];
    }else {
        //2秒后，兜底判断当前是否属于经期身份
        @weakify(self);
        imy_asyncBlock(2, ^{
            @strongify(self);
            if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModeNormal) {
                [self requestAdData];
            }
        });
    }
}

- (void)requestAdData {
    // 头部广告
    [self.adManager refreshData];
    // 贴边广告
    [self.sideAdManager refreshData];
    //二楼广告
//    [self.twoFloorAdManager refreshData];
}

- (void)requestCardData {
    NSString *path = [NSString stringWithFormat:@"v4/jingqi"];
    @weakify(self);
    [[[IMYServerRequest postPath:path host:diaries_seeyouyima_com params:nil headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
        @strongify(self);
        self.cardDataDict = x.responseObject;
        self.theoreticalTemplateID = [self.cardDataDict.allValues.firstObject valueForKey:@"template_id"] ?: @(0);
        
        //保存数据作缓存
        [[IMYCacheHelper sharedCacheManager] setObject:self.cardDataDict forKey:@"CardHomeDefaultDataKey"];
        
        [self.tableViewAdapter refreshModulesWithData:self.cardDataDict completedBlock:^{
            @strongify(self);
            [self.tableViewAdapter reloadModules];
            [self.tableView imy_headerEndRefreshing];
            [self.navBarManager tabStopRefresh];
            /// 刷新搜索栏关键字
            [self.navBarManager refreshSearchSuggestWords];
                /// 请求大社区接口
#if   __has_include("IMYMixFeedsAdapter.h")
            if (!self.feedsAdapter.adapter.refreshAfterLaunch) {
                [self.feedsAdapter.adapter requestData];
            }
#endif
            // 刷新广告接口
//            [self.adAdapter requestData];
        }];
        [self postTemplateEvent:YES];
    } error:^(NSError *error) {
        @strongify(self);
        imy_throttle(1, ^{
            [IMYErrorTraces postWithType:IMYErrorTraceTypeAPIFails
                                pageName:@"SYHomeViewController"
                                category:IMYErrorTraceCategoryJingqi
                                 message:[NSString stringWithFormat:@"v4/jingqi——%ld",error.code >= 0? 5000: 6000]
                                  detail:@{
                                           @"code":@(error.code),
                                           @"reason" : error.localizedFailureReason ?: error.localizedDescription
                                         }];
        });

        [self.navBarManager tabStopRefresh];
        [self.tableView imy_headerEndRefreshing];
        [self postTemplateEvent:NO];
#if   __has_include("IMYMixFeedsAdapter.h")

        if (self.feedsAdapter.adapter) {
            [self.feedsAdapter.adapter showRequestFail];
        }
#endif
    }];
}

#pragma mark - 大社区首页
/// 大社区首页频道实验
- (BOOL)useTabFeeds {
//    实验配置地址： 控制组：1（展示）实验组：0（不展示） 默认：1（展示） 身份隔离域：0（不展示） 业务隔离域：0（不展示）
    return YES;
}
/// 知识模块home_page曝光事件
- (void)setupKnowBiView {
#if   __has_include("IMYMixFeedsAdapter.h")
    [self.feedsAdapter.adapter BiViewWithTableView:self.tableView];
#endif
}

#pragma mark - ga page

- (NSString *)ga_pageName {
    return @"IMYMixHome";
}


/// 上报首页模板埋点事件
/// @param succeed 请求模板是否成功
- (void)postTemplateEvent:(BOOL)succeed {
    if (self.hasTemplateIDPost) {
        //只需上报一次
        return;
    }
    self.hasTemplateIDPost = YES;
    NSString *theoreticalTemplateID = succeed ? [self.theoreticalTemplateID stringValue]: @"yc";
    if ([theoreticalTemplateID isEqualToString:@"0"]) {
        theoreticalTemplateID = @"yc";
    }
    NSString *realTemplateID = succeed ? [self.theoreticalTemplateID stringValue]: [self.realTemplateID stringValue];
    NSString *public_type = [NSString stringWithFormat:@"ll_%@-zs_%@", theoreticalTemplateID, realTemplateID];
    
    NSDictionary *params = @{@"event": @"jq_sy_xfmb_mbcj",
                             @"public_type": public_type,
                             @"action": @(1)
    };
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

#pragma mark - UIDelegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {

}

- (void)scrollToFeeds {
    CGFloat offset = 0;
#if   __has_include("IMYMixFeedsAdapter.h")

    if (_feedsAdapter && self.tableView.numberOfSections ) {
        offset = [self.feedsAdapter.adapter.module rectForSection:0].origin.y - 8;
    }
#endif
    if (self.tableView.contentOffset.y < offset) {
        [self.tableView setContentOffset:CGPointMake(0, offset) animated:YES];
    }
}

#pragma mark - 广告
//candisplay:广告弹窗是否能正常显示(防止与口令弹窗重叠)
- (void)initChaPingAdmanager:(BOOL)canDisplay {
    //接入tab插屏广告
    if (self.chapingAd) {
        return; //插屏已经初始化过
    }
    @weakify(self);
    [self bk_performBlock:^(id obj) {
        @strongify(self);
        if ([IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeNormal) {
            // 非经期身份不展示广告，出现bug首次安装经期切换到备孕身份，无法展示插屏，因为备孕身份的adinfo被该self.chapingAd覆盖，viewcontroller传递错误
            return;
        }
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kIMYHomeChaPingAdNotification" object:@{@"havePopupAd": @(YES)}];
        void (^adShowBlock)(BOOL isShow) = ^(BOOL isShow) {
            if (!isShow) {
                imy_asyncMainBlock(0.2, ^{
                    [[NSNotificationCenter defaultCenter] postNotificationName:@"kIMYHomeChaPingAdNotification" object:@{@"havePopupAd": @(NO)}];
                });
            }
        };
        IMYAdvertiserInfo* adInfo  = [IMYAdvertiserInfo adInfoWithSource:nil page:IMYADPageChaPing position:IMYADPositionChaPingHome userInfo:nil viewController:self];
        [adInfo unlock];
        [adInfo appendUserInfo:@{@"canDisplay" : @(YES),
                                 @"adShowBlock" : adShowBlock

        }];
        [adInfo lock];
        self.chapingAd = [[IMYAdFactories getAdManagerFactory] getPopupAdManagerWithADInfo:adInfo];
        [self.chapingAd refreshData];
        
        //监听放在插屏广告初始化之后
        [[NSNotificationCenter defaultCenter] postNotificationName:@"IMYRecordHomeChapingAdFinishInit" object:nil];
        
    } afterDelay:0.3];
}

- (void)initHomeAd:(BOOL)useTabFeeds {
    NSMutableDictionary *userInfo = [[NSMutableDictionary alloc] init];
    userInfo[@"custom_flag"] = @"jingqi_card_mixfeeds_home";
    userInfo[@"mixfeeds_channel"] = @(useTabFeeds);
    if (_msgBannerAdapter) {
        [userInfo addEntriesFromDictionary:[_msgBannerAdapter getAdUserInfo]];
    }
    IMYAdvertiserInfo *adInfo = [IMYAdvertiserInfo adInfoWithSource:nil page:IMYADPageJingQiHomeCard position:0 userInfo:userInfo viewController:self];
    self.adManager = [[IMYAdFactories getAdManagerFactory] getAdapterAdManagerWithADInfo:adInfo];
    self.adManager.tableView = self.tableView;
}

//- (void)initTwoFloorAd {
//    // 是否使用 v2
//    if (self.useRefreshHeaderV2) {
//        return;
//    }
//    
//    //广告回调block
//    self.adSignal = [IMYAdSignal adSignalWithPage:IMYADPageHome userInfo:nil];
//    self.startAnimationSignal = [IMYAdSignal adSignalWithPage:IMYADPageHome userInfo:nil];
//    self.endShowAnimationSignal = [IMYAdSignal adSignalWithPage:IMYADPageHome userInfo:nil];
//     
//    @weakify(self);
//    void (^adDataBlock)(NSDictionary *admodel) = ^void(NSDictionary *admodel) {
//        @strongify(self);
//        self.refreshHeader.adModel = admodel;
//    };
//    void (^showAdBlock)(NSDictionary *adVCFrame) = ^void(NSDictionary *adVCFrame) {
//        @strongify(self);
//        [self.adSignal sendData:adVCFrame];
//    };
//    
//    void (^startAnimationBlock)(BOOL show) = ^void(BOOL show) {
//        @strongify(self);
//        [self.startAnimationSignal sendData:@(show)];
//    };
//    
//    void (^endShowAnimationBlock)(NSDictionary *info) = ^void(NSDictionary *info) {
//        @strongify(self);
//        [self.endShowAnimationSignal sendData:nil];
//    };
//    
//    void (^prepareToShowAnimationBlock)(BOOL show,BOOL isBackGround) = ^void(BOOL show,BOOL isBackGround) {
//        @strongify(self);
//        if (self.refreshHeader.prepareToShowAdAnimationBlock) {
//            self.refreshHeader.prepareToShowAdAnimationBlock(show,isBackGround);
//        }
//    };
//    
//    BOOL (^guideAnimationBlock)(void) = ^BOOL() {
//        @strongify(self);
//        return self.showRecordGuide;
//    };
//    
//    NSDictionary *userInfo = @{
//        @"custom_flag": @"two_floor",
//        @"adSignal":self.adSignal,
//        @"dropRefreshAdBlock":adDataBlock,
//        @"startAnimationSignal":self.startAnimationSignal,
//        @"prepareToShowAnimationBlock":prepareToShowAnimationBlock,
//        @"endShowAnimationSignal":self.endShowAnimationSignal,
//        @"guideAnimationBlock":guideAnimationBlock
//    };
//    
//    IMYAdvertiserInfo *adInfo = [IMYAdvertiserInfo adInfoWithSource:nil page:IMYADPageHome position:0 userInfo:userInfo viewController:self];
//    self.twoFloorAdManager = [[IMYAdFactories getAdManagerFactory] getAdapterAdModuleWithADInfo:adInfo];
//    [self.twoFloorAdManager setTableView:self.tableView];
//    self.refreshHeader.showAdBlock = showAdBlock;
//    self.refreshHeader.startAnimationBlock = startAnimationBlock;
//    self.refreshHeader.endShowAnimationBlock = endShowAnimationBlock;
//}

- (void)initSideAD:(BOOL)useTabFeeds {
    NSMutableDictionary *sideUserInfo = [[NSMutableDictionary alloc] init];
    sideUserInfo[@"shouldSide"] = @(YES);
    sideUserInfo[@"mixfeeds_channel"] = @(useTabFeeds);
    IMYAdvertiserInfo *sideAdInfo = [IMYAdvertiserInfo adInfoWithSource:nil page:IMYADPageSide position:IMYADPositionUnpregnancySidebar userInfo:sideUserInfo viewController:self];
    self.sideAdManager = [[IMYAdFactories getAdManagerFactory] getTableViewAdManagerWithADInfo:sideAdInfo];
    [self.sideAdManager setTableView:self.tableView];
}

- (void)updateSideAdSubView:(UITableView*)tableView {
    NSMapTable *mapTable = [NSMapTable weakToWeakObjectsMapTable];
    [mapTable setObject:tableView forKey:@"subview_tableview"];
#if   __has_include("IMYMixFeedsAdapter.h")
    BOOL isRefreshStyle = [self.feedsAdapter refreshStyle];
    [self.sideAdManager.adInfo unlock];
    [self.sideAdManager.adInfo appendUserInfo:@{@"subview_tableview_map": mapTable,@"refreshStyle":@(isRefreshStyle)}];
    [self.sideAdManager.adInfo lock];
#endif
}

- (NSDictionary*)mixFeedsAdUserInfo {
    NSDictionary *adUserInfo = nil;
    if ([self useTabFeeds]) {
        adUserInfo = @{@"custom_flag":@"jinqi_card_channel_mixfeeds_home"};
    }else {
        adUserInfo = @{@"custom_flag":@"jingqi_card_mixfeeds"};
    }
    return adUserInfo;
}

//MARK: - getter

- (IMYRecordDateSwitchBannerAdapter<IMYTableViewAdapterModuleDelegate> *)dateSwitchBannerAdapter {
    if(!_dateSwitchBannerAdapter) {
        _dateSwitchBannerAdapter = [[IMYRecordDateSwitchBannerAdapter alloc] initWithTableView:self.tableView];
        @weakify(self);
        _dateSwitchBannerAdapter.showGuideBlock = ^{
            @strongify(self);
            [self showGuideView];
        };
        _dateSwitchBannerAdapter.clickOtherHideGuideBlock = ^{
            @strongify(self);
            if ([SYABTestManager newUserGuideABTestShow2]) {
                imy_asyncMainBlock(0.2, ^{
                    [self registerRobotForRecordTab:SYHomeAlertPriorityRecordTab];
                    [self showGuideView];
                });
            }
        };
    }
    return _dateSwitchBannerAdapter;
}

#pragma mark - 金豆任务跳转
- (void)jindouTaskToHome {
    [self scrollToFeeds];
}

// MARK: - 首页二楼（893 新增）
/// 添加刷新控件
- (void)addTableViewRefreshHeaderV2 {
    @weakify(self);
    if (self.refreshHeaderV2) {
        self.tableView.mj_header = self.refreshHeaderV2;
        return;
    }
    
    self.refreshHeaderV2 = [IMYCK2ndFRefreshHeaderV2 headerWithRefreshingBlock:^{
        @strongify(self);
        //卡片化内容
        [self refreshData];
#if   __has_include("IMYMixFeedsAdapter.h")
        if (self.feedsAdapter.adapter.bi_refreshType != 2) {
            self.feedsAdapter.adapter.bi_refreshType = 3;
        }
        [self.feedsAdapter.adapter requestData];
#endif
        //会员免广告toast
        imy_asyncMainBlock(0.7, ^{
            [[IMYURIManager sharedInstance] runActionWithString:@"tips/advertisingFreeMembership"];
        });

        //bi report
        NSDictionary *params = @{@"event":@"yy_sy_xlsx",
                                 @"action":@(1),
                                 @"public_type":@"妈妈页"};
        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
    }];
    // refreshHeader.isUserModeType = YES;
    self.tableView.mj_header = self.refreshHeaderV2;
}

/// 二楼数据（首次请求）
- (void)reloadUdimDatasIfNeed {
    if (!self.isViewActived) {
        return;
    }
    
    [self.refreshHeaderV2 req2ndFloorDataIfNeed];
}

/// 配置中心变化
- (void)handleConfigloadedFor2ndFloor {
    [self reloadUdimDatasIfNeed];
}

@end

@implementation SYHomeViewController (AlertShowManager)

- (void)showGuideView {
    imy_asyncMainBlock(^{
        [[IMYAlertShowManager sharedInstance] showAlerts];
    });
}

/// 注册机器人
- (void)registerAlertRobot {
    /// 经期模式新用户可以获得更好的首次体验【AB实验】
    BOOL newUserGuideABTestShow1 = [SYABTestManager newUserGuideABTestShow1];
    BOOL newUserGuideABTestShow2 = [SYABTestManager newUserGuideABTestShow2];
    if (newUserGuideABTestShow1) {
        /// 先引导至记录tab，回到首页后引导至今日密报
        [self registerRobotForRecordTab:SYHomeAlertPriorityBanner];
        [self.dateSwitchBannerAdapter registerRobotForBanner:SYHomeAlertPriorityRecordTab onlyFirstly:![SYHomeRecordCoverView needShow] vc:self];
    } else if (newUserGuideABTestShow2) {
        /// 先引导至今日密报，回到首页后引导至记录tab
        [self.dateSwitchBannerAdapter registerRobotForBanner:SYHomeAlertPriorityBanner onlyFirstly:[self dateSwitchBannerIsOnlyFirstly] vc:self];
        if (![IMYRecordCoverView needShow]) {
            [self registerRobotForRecordTab:SYHomeAlertPriorityRecordTab];
        }
    } else {
        /// 今日密报卡片蒙层
        [self.dateSwitchBannerAdapter registerRobotForBanner:SYHomeAlertPriorityBanner onlyFirstly:YES vc:self];
    }
    /// 亲友模式弹窗
    [self registerRobotForQinyou];
    @weakify(self);
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:kQinyouModeChangeAlterNotification object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *object) {
        @strongify(self);
        [self registerRobotForQinyou];
    }];
}

/// 方便后续热更新
- (BOOL)dateSwitchBannerIsOnlyFirstly {
    return YES;
}

- (void)registerRobotForQinyou {
    IMYURI *partnerUri = [IMYURI uriWithPath:@"alert/robot/qinyou" params:@{@"pageVC": self, @"priority": @(SYHomeAlertPriorityPartner)} info:nil];
    [[IMYURIManager sharedInstance] runActionWithURI:partnerUri];
}

#pragma mark - 记录引导蒙层
- (void)registerRobotForRecordTab:(CGFloat)priority {
    if (![SYHomeRecordCoverView needShow]) {
        return;
    }
    /// 获取弹窗自动化机器人
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:[SYHomeRecordCoverView robotKey]];
    /// 已有弹窗监控
    if (alertRobot.isReady) {
        if (self != alertRobot.alertAction.inPageVC) {
            alertRobot.DismissNoCallback();
            alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:[SYHomeRecordCoverView robotKey]];
        } else {
            return;
        }
    }
    alertRobot.DisableAutoNext();
    alertRobot.PageVC(self);
    /// 注册弹窗队列
    @weakify(self);
    alertRobot.Priority(priority).TabHomeType(SYTabBarIndexTypeHome).IsBaseInvalid(^BOOL{
        return ![SYHomeRecordCoverView needShow];
    }).CreateRealAlertView(^UIView<IMYAlertShowViewProtocol> *{
        SYBaseTabBarController *tabVC = [SYBaseTabBarController shareTabbarController];
        NSInteger tabCount = tabVC.tabBar.items.count;
        //计算 显示区域
        CGRect coverShowFrame = CGRectMake(SCREEN_WIDTH/tabCount + (SCREEN_WIDTH/tabCount - 52)/2, SCREEN_HEIGHT - SCREEN_TABBAR_HEIGHT, 52, 52);
        SYHomeRecordCoverView *alertView = [[SYHomeRecordCoverView alloc] initWithFrame:[UIWindow imy_getShowTopWindow].bounds pathRect:coverShowFrame];
        alertView.hidden = YES;
        [alertView bk_whenTapped:^{
            @strongify(self);
            [self hiddenRecordTabView];
            /// 若未离开首页，直接点击关闭蒙层，需立即展示第二个引导
            imy_asyncMainBlock(0.2, ^{
                if (![self.dateSwitchBannerAdapter isBaseInvalidForBanner]) {
                    [self showGuideView];
                }
            });
        }];
        //跳转 点击
        alertView.hitTestBlock = ^{
            @strongify(self);
            [self hiddenRecordTabView];
            [SYBaseTabBarController shareTabbarController].selectedTabIndexType = SYTabBarIndexTypeRecord;
            [self.navigationController popToRootViewControllerAnimated:NO];
        };
        @weakify(alertView);
        alertView.willShowBlock = ^{
            @strongify(alertView);
            alertView.alpha = 0;
            alertView.hidden = NO;
            [UIApplication.sharedApplication.keyWindow addSubview:alertView];
        };
        return alertView;
    }).Ready();
}

- (void)hiddenRecordTabView {
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:[SYHomeRecordCoverView robotKey]];
    alertRobot.DisableAutoNext();
    alertRobot.Dismiss();
}

#pragma mark - 青少年模式经期首页弹窗相关方法
#pragma mark SYYouthModePopupTipCoverView 弹窗
- (void)showYouthModeTopTipBubbleIfNeeded:(CGFloat)delaySeconds {
    NSString *queueKey = [NSString stringWithFormat:@"showYouthModeTopTipBubble_%p", self];
    /// 标准滤重写法
    @weakify(self);
    imy_throttle_on_queue(delaySeconds, queueKey, dispatch_get_main_queue(), ^{
        @strongify(self);
        [self registerRobotForYouthNew];
    });
}

- (void)registerRobotForYouthNew {
    if (![SYYouthModePopupTipCoverView needShow]) {
        return;
    }
    /// 获取弹窗自动化机器人
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:[SYYouthModePopupTipCoverView robotKey]];
    /// 已有弹窗监控
    if (alertRobot.isReady) {
        if (self != alertRobot.alertAction.inPageVC) {
            alertRobot.DismissNoCallback();
            alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:[SYYouthModePopupTipCoverView robotKey]];
        } else {
            return;
        }
    }
    alertRobot.PageVC(self);
    /// 注册弹窗队列
    @weakify(self);
    alertRobot.Priority(SYHomeAlertPriorityYouthNew).TabHomeType(SYTabBarIndexTypeHome).IsBaseInvalid(^BOOL{
        return ![SYYouthModePopupTipCoverView needShow];
    }).CreateRealAlertView(^UIView<IMYAlertShowViewProtocol> *{
        @strongify(self);
        UIWindow *keyWindow = UIApplication.sharedApplication.keyWindow;
        NSInteger youngButtonTag = 709394;
        UIView *youngButton = [self.navBarManager.navBar viewWithTag:youngButtonTag];
        CGRect rect = youngButton.frame;
        rect = CGRectMake(rect.origin.x - 3,
                          rect.origin.y - 3,
                          rect.size.width + 6,
                          rect.size.height + 6);
        
        SYYouthModePopupTipCoverView *alertView = [[SYYouthModePopupTipCoverView alloc] initWithFrame:keyWindow.bounds pathRect:rect];
        alertView.hidden = YES;
        
        [alertView bk_whenTapped:^{
            @strongify(self);
            [self hiddenYouthNewView];
        }];
        @weakify(alertView);
        alertView.willShowBlock = ^{
            @strongify(alertView);
            alertView.alpha = 0;
            alertView.hidden = NO;
            [UIApplication.sharedApplication.keyWindow.rootViewController.view addSubview:alertView];
        };
        return alertView;
    }).Ready();
}

- (void)hiddenYouthNewView {
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:[SYYouthModePopupTipCoverView robotKey]];
    alertRobot.DisableAutoNext();
    alertRobot.Dismiss();
}

#pragma mark 业务底部picker弹窗
- (void)showYouthModeBottomDialogIfNeeded:(CGFloat)delaySeconds {
    NSString *queueKey = [NSString stringWithFormat:@"showYouthModeBottomDialog_%p", self];
    // 标准滤重写法
    @weakify(self);
    imy_throttle_on_queue(delaySeconds, queueKey, dispatch_get_main_queue(), ^{
        @strongify(self);
        [self showYouthModeBottomDialogIfNeeded];
    });
}

- (void)showYouthModeBottomDialogIfNeeded {
    @weakify(self);
    [SYYoutModeConfigManager isHomeYouthBottomDialogNeedsToDisplay:^(BOOL isNeeded) {
        imy_asyncMainBlock(^{
            @strongify(self);
            if (!isNeeded) {
                return;
            }
            [self registerRobotForYouthPicker];
        });
    }];
}

- (void)registerRobotForYouthPicker {
    /// 获取弹窗自动化机器人
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:[SYYouthModeActionDialog robotKey]];
    /// 已有弹窗监控
    if (alertRobot.isReady) {
        if (self != alertRobot.alertAction.inPageVC) {
            alertRobot.DismissNoCallback();
            alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:[SYYouthModeActionDialog robotKey]];
        } else {
            return;
        }
    }
    alertRobot.PageVC(self);
    /// 注册弹窗队列
    @weakify(self);
    alertRobot.Priority(SYHomeAlertPriorityYouthPicker).TabHomeType(SYTabBarIndexTypeHome).IsBaseInvalid(^BOOL{
        return NO;
    }).CreateRealAlertView(^UIView<IMYAlertShowViewProtocol> *{
        id confirmBlock = ^(NSDictionary *info) {
            @strongify(self);
            [[IMYURIManager sharedInstance] runActionWithPath:@"youthmode/detail_page" params:@{@"entrance": @(31)} info:nil];
            /// 点击统计
            [self biYouthPickerWithAction:2 publicType:@"点击青少年模式按钮"];
        };
        
        id cancelBlock = ^(NSDictionary *info) {
            @strongify(self);
            /// 点击统计
            [self biYouthPickerWithAction:2 publicType:@"取消"];
        };
        SYBaseTabBarController *tabVC = [SYBaseTabBarController shareTabbarController];
        SYYouthModeActionDialog *dialog = [SYYouthModeActionDialog actionDialogInView:tabVC.view confirmBlock:confirmBlock cancelBlock:cancelBlock];
        dialog.willShowBlock = ^{
            @strongify(self);
            [SYYoutModeConfigManager setHomeBottomYouthDialogShownTime:[NSDate date]];
            // 曝光统计
            [self biYouthPickerWithAction:1 publicType:@""];
        };
        return dialog;
    }).Ready();
}

- (void)biYouthPickerWithAction:(NSInteger)action publicType:(NSString *)publicType {
    NSMutableDictionary *params = @{
        @"event" : @"wd_qsnms_kg",
        @"action": @(action),
        @"entrance": @(31),
        @"public_info": @"开启青少年模式",
        @"position": @(165)
    }.mutableCopy;
    if (imy_isNotBlankString(publicType)) {
        [params imy_setNonNilObject:publicType forKey:@"public_type"];
    }
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

- (void)hiddenYouthPicker {
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:[SYYouthModeActionDialog robotKey]];
    alertRobot.DisableAutoNext();
    alertRobot.Dismiss();
}

@end
