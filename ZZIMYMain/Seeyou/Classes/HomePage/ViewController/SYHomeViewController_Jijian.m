//
//  SYHomeViewController_<PERSON>jian.m
//  ZZIMYMain
//
//  Created by ss on 2024/3/21.
//

#import <Foundation/Foundation.h>
#import "SYBaseTabBarController.h"
#import "SYHomeViewController_<PERSON><PERSON><PERSON>.h"

#import <IMYCommonKit/IMYCommonPageEvent.h>
#import <IMYCommonKit/IMYCKRefreshHeader.h>
#import <IMYCommonKit/IMYNestTableView.h>
#import <IMYBaseKit/IMYTableViewAdapter.h>
#import <IMYRecord/IMYRecordDateSwitchBannerAdapter.h>
#import <IMYRecord/IMYTodayForecastAdapter.h>
#import "IMYTodayForecastAdapter.h"
#import <IMYCommonKit/IMYHPVAdapter.h>
#import <IMYCommonKit/IMYModuleConfigManager.h>
#import <IMYCommonKit/IMYCKDynamicNavBarManager.h>
#import <IMYMSG/IMYMsgBannerAdapter.h>
#import <IMYBaseKit/IMYGlobalColorManager.h>
#import <IMYMSG/IMYMsg2Manager.h>

#import <IMYRecord/IMYRecordDefines.h>
#import <IMYBaseKit/UITabBarItem+IMYTabBarItemManager.h>
#import "IMYRecordHomeToolsAdapter.h"
#import "IMYYunYuABManager.h"
#import "IMYRHealthAnalysisPeriodAdapter.h"
#import "IMYRecordHomeHealthAdapter.h"
#if   __has_include("IMYUGCHomeAdapter.h")
#import "IMYUGCHomeAdapter.h"
#endif
#import "SYQinYouChangeModeManager.h"

#if __has_include(<IMYMSG/IMYMSG.h>)
#import <IMYMSG/IMYMsgNotifyManager.h>
#endif

@interface SYHomeViewController_Jijian() <UITableViewDelegate,UIScrollViewDelegate>
@property (nonatomic, strong) IMYCKDynamicNavBarManager *navBarManager;
@property (nonatomic, strong) IMYNestTableView *tableView;

@property (nonatomic, strong) IMYTableViewAdapter *tableViewAdapter;

@property (nonatomic, strong) IMYRecordDateSwitchBannerAdapter<IMYTableViewAdapterModuleDelegate> *dateSwitchBannerAdapter;
@property (nonatomic, strong) IMYRecordHomeToolsAdapter<IMYTableViewAdapterModuleDelegate> *toolsAdapter;
@property (nonatomic, strong) IMYRHealthAnalysisPeriodAdapter *periodAdapter;//周期变化
@property (nonatomic, strong) IMYTodayForecastAdapter<IMYTableViewAdapterModuleDelegate> *todayForecastAdapter;  //今日预测
@property (nonatomic, strong) IMYRecordHomeHealthAdapter<IMYTableViewAdapterModuleDelegate> *jingQiHealthAdapter;  //经期健康度

@property (nonatomic, strong) NSDictionary *cardDataDict; //卡片化数据，由接口获取
@property (nonatomic, assign) BOOL needRequest;                   //需求重新请求接口

//BI相关
@property (nonatomic, assign) BOOL hasTemplateIDPost;                   //模板ID是否上报过
@property (nonatomic, strong) NSNumber *realTemplateID;                 //真实模板ID
@property (nonatomic, strong) NSNumber *theoreticalTemplateID;          //理论模板ID

//广告相关
@property (nonatomic, strong) IMYCKRefreshHeader *refreshHeader;
@property (nonatomic, strong) IMYLineView *lineView;

@end


@implementation SYHomeViewController_Jijian

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    [self.tabBarItem imy_setFinishedSelectedImageName:@"all_bottommeetyou_up" withFinishedUnselectedImageName:@"all_bottommeetyou"];
    self.tabBarItem.imageInsets = UIEdgeInsetsMake(-5, 0, 5, 0);
    self.tabBarItem.titlePositionAdjustment = UIOffsetMake(0, -1.5);
    [self resetTabName];
    return self;
}

- (void)resetTabName {
    //https://www.tapd.cn/21039721/bugtrace/bugs/view/1121039721001127315
    //https://www.tapd.cn/21039721/bugtrace/bugs/view/1121039721001126958
    //以下两个设置title都需要
    NSString *title = IMYString(@"美柚");
    self.navBarManager.customerNormalTitle = title;
  
    if (SYBaseTabBarController.shareTabbarController.tabBar.items.count > 0) {
        SYBaseTabBarController.shareTabbarController.tabBar.items[0].title = title;
        NSLog(@"bindingVC isViewActived   settname");
    }
    if ([SYBaseTabBarController shareTabbarController].selectedIndex != 0) {
        /// commonKit那边需要页面isViewActived变化才会更新title，当首页未被选中时，固定展示美柚
        self.tabBarItem.title = IMYString(@"美柚");
    } else {
        self.tabBarItem.title = title;
    }
}

- (BOOL)isNavigationBarHidden {
    return YES;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [[IMYCommonPageEvent shareManager] pageEventDidAppear];
    if (self.needRequest) {
        [self refreshData];
        self.needRequest = NO;
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self loadDefaultCardData];
    
    //UI
    self.navBarManager = [IMYCKDynamicNavBarManager new];
#if   __has_include("IMYUGCHomeAdapter.h")
    self.navBarManager.downBarIconStyle = [IMYUGCHomeAdapter tabbarIconDownStyle];
#endif
    self.navBarManager.bindingVC = self;
    [self updateNavBarConfig];
    [self.view addSubview:self.navBarManager.navBar];
    [IMYGlobalColorManager registView:self.navBarManager.navBar style:IMYGlobalColorStyleHomeFirstVisible];
    [IMYGlobalColorManager registView:self.navBarManager.navBar style:IMYGlobalColorStyleHomeHalf];
    
    self.tableView = [[IMYNestTableView alloc] initWithFrame:self.view.bounds];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [self.tableView imy_setBackgroundColorForKey:kIMY_BG];
    self.tableView.estimatedRowHeight = 0;
    self.tableView.estimatedSectionHeaderHeight = 0;
    self.tableView.estimatedSectionFooterHeight = 0;
    self.tableView.supportNoCeiling = YES;
    self.tableView.imy_top = SCREEN_NAVIGATIONBAR_HEIGHT;
    self.tableView.imy_height = SCREEN_HEIGHT - SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - SCREEN_TABBAR_HEIGHT + 8;
    [self.view addSubview:self.tableView];
    self.tableView.tableFooterView = [self tableViewFootview];
//    [self addTableViewRefreshHeader];
    [IMYGlobalColorManager registView:self.tableView style:IMYGlobalColorStyleHomeFirstVisible];
    
    //Adapter
    self.tableViewAdapter = [IMYTableViewAdapter adpaterWithTableView:self.tableView];
    self.tableViewAdapter.UIDelegate = self;
    
    //dateSwitchBannerAdapter,msgBannerAdapter同一个key(jq_top_banner)
    [self.tableViewAdapter registerModuleDelegate:self.dateSwitchBannerAdapter];
    
    self.toolsAdapter = IMYRecordHomeToolsAdapter.new;
    [self.tableViewAdapter registerModuleDelegate:self.toolsAdapter];
    [self.tableViewAdapter registerModuleDelegate:self.periodAdapter];
    [self.tableViewAdapter registerModuleDelegate:self.todayForecastAdapter];
    [self.tableViewAdapter registerModuleDelegate:self.jingQiHealthAdapter];
    
    [self.view addSubview:self.lineView];
    @weakify(self);

    // 初始化新知识埋点View
    [self.tableViewAdapter refreshModulesWithData:self.cardDataDict completedBlock:^{
        @strongify(self);
        [self.tableViewAdapter reloadModules];
    }];
     
    [self resetTabName];
    // 添加各种监听
    [self addNotification];
     
    //取默认数据，刷新
    [self loadDefaultCardData];
    
    /// 请求数据
    [self refreshData];
    
    // 底部提示
    [self setupFooterTipsView];
    [self registerAlertRobot];
}

- (void)setupFooterTipsView {
    UIView *tableFooterView = [UIView new];
    // 设计要求 50pt，但是最底部的cell 会有边距 8，所以这边只要 42 即可
    tableFooterView.imy_size = CGSizeMake(SCREEN_WIDTH, 42);
    
    UILabel *label = [UILabel new];
    label.text = @"可在“我-设置-极简模式”中切回标准版首页";
    label.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [label imy_setTextColor:kCK_Black_B];
    [label imy_sizeToFit];
    label.center = tableFooterView.imy_selfcenter;
    // 原因同上，需要减去偏移 8pt
    label.imy_centerY -= 8;
    [tableFooterView addSubview:label];
    
    self.tableView.tableFooterView = tableFooterView;
}

/// 注册机器人
- (void)registerAlertRobot {
    [self registerRobotForQinyou];
    @weakify(self);
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:kQinyouModeChangeAlterNotification object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *object) {
        @strongify(self);
        [self registerRobotForQinyou];
    }];
}

- (void)registerRobotForQinyou {
    /// 亲友模式弹窗
    IMYURI *partnerUri = [IMYURI uriWithPath:@"alert/robot/qinyou" params:@{@"pageVC": self, @"priority": @(90)} info:nil];
    [[IMYURIManager sharedInstance] runActionWithURI:partnerUri];
}

- (void)showGuideView {
    imy_asyncMainBlock(^{
        [[IMYAlertShowManager sharedInstance] showAlerts];
    });
}

- (IMYLineView *)lineView{
    if (!_lineView) {
        IMYLineView *view = [[IMYLineView alloc] initWithFrame:CGRectMake(0, SCREEN_NAVIGATIONBAR_HEIGHT, SCREEN_WIDTH, 1/SCREEN_SCALE)];
        view.colorKey = kCK_Black_J;
        view.alpha = 0;
        _lineView = view;
    }
    return _lineView;
}
//scrollView 代理目的处理collectionView 上下延展View的 背景色
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat offsetY = scrollView.contentOffset.y;
    
    if (offsetY > 0) {
        if (offsetY < 4) {
            CGFloat alpha = offsetY / 4.0;
            self.lineView.alpha = alpha;
        } else {
            self.lineView.alpha = 1;
        }
    } else {
        self.lineView.alpha = 0;
    }
}
- (void)loadDefaultCardData {
    //取默认数据，刷新
    NSDictionary *dataDict = [[NSUserDefaults standardUserDefaults] objectForKey:[self cacheKey]];
    if (!dataDict) {
        dataDict = [[IMYCacheHelper sharedCacheManager] objectForKey:[self cacheKey]];
    } else {
        // 升级上来迁移老的存储对象
        [[IMYCacheHelper sharedCacheManager] setObject:dataDict forKey:[self cacheKey]];
        [[IMYUserDefaults standardUserDefaults] removeObjectForKey:[self cacheKey]];
    }
    if (!dataDict || dataDict.count == 0) {
        NSString *path = [[NSBundle mainBundle] pathForResource:@"CardHomeDefaultData" ofType:@"json"];
        NSData *defaultData = [[NSData alloc] initWithContentsOfURL:[NSURL fileURLWithPath:path]];
        NSDictionary *dict = [defaultData imy_jsonObject];
        dataDict = [dict objectForKey:@"data"];
    }
    self.cardDataDict = dataDict;
    self.realTemplateID = [self.cardDataDict.allValues.firstObject valueForKey:@"template_id"] ?: @(0);
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [self.navBarManager tabStopRefresh];
    [super viewWillDisappear:animated];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self.dateSwitchBannerAdapter hiddenBriefReportWhenDisappear];
}

- (void)addNotification {
    @weakify(self);
    //tab名称刷新
    RACSignal *personalRecommandChangeSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:@"IMYPersonalRecommandStateChanged" object:nil];
    RACSignal *youngModeChangeSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:@"IMYYouthModeStateChanged" object:nil];

    [[[RACSignal merge:@[[IMYPublicAppHelper shareAppHelper].useridChangedSignal, personalRecommandChangeSignal, youngModeChangeSignal]] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
       @strongify(self);
        [self resetTabName];
    }];
    
    //tab旋转结束
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"kIMYNewsRootContainerRefreshKey" object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *object) {
        [[IMYURIManager shareURIManager] runActionWithURI:[IMYURI uriWithPath:@"home/showBadgesCount" params:@{@"badgesCount": @(0)} info:nil]];
    }];
    
    //重新刷新页面
    //开关值变更
    RACSignal *configChangeSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:IMYModuleConfigChangeNotification object:nil];
    //用户年龄变更
    RACSignal *userBirthdayChangedSignal = [IMYPublicAppHelper shareAppHelper].userBirthdayChangedSignal;
    //用户ID变更
    RACSignal *useridChangedSignal = [IMYPublicAppHelper shareAppHelper].useridChangedSignal;
    //日历刷新
    RACSignal *recordReloadSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:kNotificationRecordCalendarReload object:nil];
    RACSignal *changeParsIntervalNotifySignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:@"kChangeParsIntervalNotify" object:nil];

    [[[[[[RACSignal merge:@[changeParsIntervalNotifySignal, configChangeSignal, userBirthdayChangedSignal, useridChangedSignal, recordReloadSignal]] takeUntil:self.rac_willDeallocSignal] throttle:1] skip:3] deliverOnMainThread] subscribeNext:^(id x) {
        @strongify(self);
        NSLog(@"-jianji-has-not");
        [self.tableViewAdapter refreshModulesWithData:self.cardDataDict completedBlock:^{
            @strongify(self);
            [self.tableViewAdapter reloadModules];
        }];
      
        if (!self.needRequest && self.isViewLoaded) {
            if (self.isViewActived) { //如果在当前页面。及时请求接口更新数据
                [self refreshData];
            }else{  // 非当前页面。等收到首页后，再请求接口更新数据
                self.needRequest = YES;
            }
        }
    }];
    
   
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:kNotificationRecordCalendarReload object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *x) {
        @strongify(self);
        if (!self.needRequest && self.isViewLoaded && !self.isViewActived) {
            self.needRequest = YES;
        }
    }];
    
    //日历数据更新完成
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:kNotificationRecordCalendarUpdateFinish object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *note) {
        @strongify(self);
        [self refreshData];
    }];
    
    [[[useridChangedSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self updateNavBarConfig];
        [self.tableView setContentOffset:CGPointZero];
    }];
    
    [[[[IMYConfigsCenter sharedInstance].loadedSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self updateNavBarConfig];
    }];
    
    [[[[IMYPublicAppHelper shareAppHelper].youngModeChangedSignal deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self updateNavBarConfig];
    }];
    
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"IMYEBDynamicHomeVC.Refresh.Notification" object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *note) {
        @strongify(self);
        [self updateNavBarConfig];
    }];
    
#if __has_include(<IMYMSG/IMYMSG.h>)
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:K_MsgActionBoxShowing_Change object:nil] deliverOnMainThread] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification *object) {
        @strongify(self);
        [self showGuideView];
    }];
#endif
}


- (void)addTableViewRefreshHeader {
    @weakify(self);
    IMYCKRefreshHeader *refreshHeader = [IMYCKRefreshHeader headerWithRefreshingBlock:^{
        @strongify(self);
        //卡片化内容
        [self refreshData];
        
        //bi report
        NSDictionary *params = @{@"event":@"yy_sy_xlsx",
                                 @"action":@(1),
                                 @"public_type":@"妈妈页"};
        [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
    }];
    refreshHeader.isUserModeType = YES;
    self.tableView.mj_header = refreshHeader;
    self.refreshHeader = refreshHeader;
}

- (void)updateNavBarConfig {
    if ([IMYPublicAppHelper shareAppHelper].useYoungMode) {
        [self.navBarManager changeRight2Young];
    } else {
        [self.navBarManager changeRight2SearchButton];
    }
    
    if ([IMYMsg2Manager isShowMsgTab]) {
        [self.navBarManager changeLeft2Type:IMYCKBarLeftType_Search];
        [self.navBarManager changeTitle2Type:IMYCKBarTitleType_TitleJijian];
        [self.navBarManager hideRightButton];
    } else {
        [self.navBarManager changeLeft2Type:IMYCKBarLeftType_Default];
        [self.navBarManager changeTitle2Type:IMYCKBarTitleType_TitleJijian];
        self.navBarManager.customerNormalTitle = IMYString(@"美柚");
    }
}

#pragma mark - Card Handle

- (void)refreshData {
    // 卡片数据
    [self requestCardData];
}


- (void)requestCardData {
    NSString *path = [NSString stringWithFormat:@"v4/jingqi_simple_mode/home"];
    @weakify(self);
    [[[IMYServerRequest postPath:path host:diaries_seeyouyima_com params:nil headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
        @strongify(self);
        self.cardDataDict = x.responseObject;
        self.theoreticalTemplateID = [self.cardDataDict.allValues.firstObject valueForKey:@"template_id"] ?: @(0);
        //保存数据作缓存
        [[IMYCacheHelper sharedCacheManager] setObject:self.cardDataDict forKey:[self cacheKey]];
        [self.tableViewAdapter refreshModulesWithData:self.cardDataDict completedBlock:^{
            @strongify(self);
            [self.todayForecastAdapter requestDataAction];
            [self.jingQiHealthAdapter requestData];
            [self.tableViewAdapter reloadModules];
            [self.tableView imy_headerEndRefreshing];
            [self.navBarManager tabStopRefresh];
            /// 刷新搜索栏关键字
            [self.navBarManager refreshSearchSuggestWords];

        }];
        [self postTemplateEvent:YES];
    } error:^(NSError *error) {
        @strongify(self);
        imy_throttle(1, ^{
            [IMYErrorTraces postWithType:IMYErrorTraceTypeAPIFails
                                pageName:@"SYHomeViewController_jijian"
                                category:IMYErrorTraceCategoryJingqi
                                 message:[NSString stringWithFormat:@"v4/jingqi_simple_mode/home——%ld",error.code >= 0? 5000: 6000]
                                  detail:@{
                                           @"code":@(error.code),
                                           @"reason" : error.localizedFailureReason ?: error.localizedDescription
                                         }];
        });

        [self.navBarManager tabStopRefresh];
        [self.tableView imy_headerEndRefreshing];
        [self postTemplateEvent:NO];
        if (NO == [IMYNetState networkEnable]) {
            [UIWindow imy_showTextHUD:IMYString(@"网络不见了，请检查网络")];
        }
    }];
}

- (NSString *)cacheKey {
    return @"CardHomeDefaultDataKey_jiJian";
}
#pragma mark - ga page


/// 上报首页模板埋点事件
/// @param succeed 请求模板是否成功
- (void)postTemplateEvent:(BOOL)succeed {
//    if (self.hasTemplateIDPost) {
//        //只需上报一次
//        return;
//    }
//    self.hasTemplateIDPost = YES;
//    NSString *theoreticalTemplateID = succeed ? [self.theoreticalTemplateID stringValue]: @"yc";
//    if ([theoreticalTemplateID isEqualToString:@"0"]) {
//        theoreticalTemplateID = @"yc";
//    }
//    NSString *realTemplateID = succeed ? [self.theoreticalTemplateID stringValue]: [self.realTemplateID stringValue];
//    NSString *public_type = [NSString stringWithFormat:@"ll_%@-zs_%@", theoreticalTemplateID, realTemplateID];
//    
//    NSDictionary *params = @{@"event": @"jq_sy_xfmb_mbcj",
//                             @"public_type": public_type,
//                             @"action": @(1)
//    };
//    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}
//MARK: - private

- (UIView *)tableViewFootview {
    UIView *bgView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 8)];
    bgView.backgroundColor = [UIColor clearColor];
    return bgView;
}
//MARK: - getter

- (IMYRecordDateSwitchBannerAdapter<IMYTableViewAdapterModuleDelegate> *)dateSwitchBannerAdapter {
    if(!_dateSwitchBannerAdapter) {
        IMYRecordDateSwitchBannerAdapter *adapter = [[IMYRecordDateSwitchBannerAdapter alloc] initWithTableView:self.tableView];
        _dateSwitchBannerAdapter = adapter;
        @weakify(self);
        _dateSwitchBannerAdapter.showGuideBlock = ^{
            @strongify(self);
        };
    }
    return _dateSwitchBannerAdapter;
}

- (IMYRHealthAnalysisPeriodAdapter *)periodAdapter{
    if(!_periodAdapter){
        _periodAdapter = [IMYRHealthAnalysisPeriodAdapter new];
        _periodAdapter.isJiJianMode = YES;
    }
    return _periodAdapter;
}


- (IMYTodayForecastAdapter<IMYTableViewAdapterModuleDelegate> *)todayForecastAdapter{
    if (!_todayForecastAdapter) {
        IMYTodayForecastAdapter *adapter = [[IMYTodayForecastAdapter alloc] init];
        _todayForecastAdapter = adapter;

    }
    return _todayForecastAdapter;
}
- (IMYRecordHomeHealthAdapter<IMYTableViewAdapterModuleDelegate> *)jingQiHealthAdapter{
    if (!_jingQiHealthAdapter) {
        _jingQiHealthAdapter = [[IMYRecordHomeHealthAdapter alloc] init];
    }
    return _jingQiHealthAdapter;
}

@end
