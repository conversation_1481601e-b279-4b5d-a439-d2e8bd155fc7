//
//  SYQuickStartPickerViewModel.h
//  ZZIMYMain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/5/13.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, SYQuickStartPickerType) {
    SYQuickStartPickerType_Gender, // [[string, string, string]]
    SYQuickStartPickerType_Date // [minDate, maxDate]
};

@interface SYQuickStartPickerItem : NSObject

@property (nonatomic, assign) NSInteger index;
@property (nonatomic, copy) NSString *text;

@end

@interface SYQuickStartPickerViewModel : NSObject <UIPickerViewDelegate, UIPickerViewDataSource>
@property (nonatomic, copy) void (^didSelectDatePickerViewBlock)(void);
@property (nonatomic, copy) NSArray *data;
@property (nonatomic, assign) SYQuickStartPickerType pickerType;
@property (nonatomic, assign) CGFloat rowHeightForComponent; //picker行高 默认60

@property (nonatomic, strong) NSMutableArray *dataArray; // UIPickerView 的数据源：[componentsArray<SYBabyInfoPickerItem>, componentsArray<SYBabyInfoPickerItem>, componentsArray<SYBabyInfoPickerItem>...]
@property (nonatomic, strong) NSMutableArray *dateRetArray;

- (instancetype)initWithPickerType:(SYQuickStartPickerType)pickerType data:(NSArray *)data;

/// 选中日期
- (NSArray *)setSelectRowsWithDate:(NSDate *)date;
/// 字符串选中
- (NSArray *)setSelectRowsWithString:(NSString *)string;

@end

NS_ASSUME_NONNULL_END
