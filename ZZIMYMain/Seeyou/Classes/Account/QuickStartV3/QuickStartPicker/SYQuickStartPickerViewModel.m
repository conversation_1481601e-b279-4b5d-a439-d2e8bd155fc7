//
//  SYQuickStartPickerViewModel.m
//  ZZIMYMain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/5/13.
//

#import "SYQuickStartPickerViewModel.h"
#import "IMYPublic.h"
#import "NSDate+IMYExtension.h"
#import <IMYBaseKit/UIFont+IMYViewKit.h>

@implementation SYQuickStartPickerItem

@end

@interface SYQuickStartPickerViewModel ()

@end

@implementation SYQuickStartPickerViewModel

- (instancetype)initWithPickerType:(SYQuickStartPickerType)pickerType data:(NSArray *)data {
    self = [super init];
    if (self) {
        self.pickerType = pickerType;
        self.data = data;
        
        self.dataArray = @[].mutableCopy;
        self.dateRetArray = @[].mutableCopy; // 年月日数据会随着滚动变化，因此采用 dateRetArray 来记录选中结果。只有设值和didSelectRow调用会修改。解决无法很好监听UIPickerView停止滚动的事件造成越界问题。
        [self loadDataArrayWithPickerType:pickerType data:data];
    }
    return self;
}

#pragma mark - Data

- (void)loadDataArrayWithPickerType:(SYQuickStartPickerType)pickerType data:(NSArray *)data {
    switch (pickerType) {
        case SYQuickStartPickerType_Gender: {
            self.dataArray = @[].mutableCopy;
            for (NSArray *theData in data) {
                NSMutableArray *items = @[].mutableCopy;
                for (NSInteger i = 0; i < theData.count; i++) {
                    SYQuickStartPickerItem *item = [SYQuickStartPickerItem new];
                    item.index = i;
                    item.text = [theData objectAtIndex:i];
                    [items addObject:item];
                }
                [self.dataArray addObject:items];
            }
        }
            break;
        case SYQuickStartPickerType_Date: {
            self.dataArray = @[].mutableCopy;
            NSDate *maxDate = [data lastObject];
            [self loadDataArrayWithYear:maxDate.year month:maxDate.month];
            SYQuickStartPickerItem *yearItem = [[self.dataArray objectAtIndex:0] lastObject];
            SYQuickStartPickerItem *monthItem = [[self.dataArray objectAtIndex:1] lastObject];
            SYQuickStartPickerItem *dayItem = [[self.dataArray objectAtIndex:2] lastObject];
            self.dateRetArray = @[yearItem, monthItem, dayItem].mutableCopy; // dateRetArray 初始化
        }
            break;
            
        default: {
            self.dataArray = @[].mutableCopy;
        }
            break;
    }
}

- (void)loadDataArrayWithYear:(NSInteger)year month:(NSInteger)month {
    if (self.pickerType != SYQuickStartPickerType_Date) {
        return;
    }
    
    NSDate *minDate = [self.data firstObject];
    NSDate *maxDate = [self.data lastObject];
    
    NSMutableArray *yearArray = @[].mutableCopy;
    NSMutableArray *monthArray = @[].mutableCopy;
    NSMutableArray *dayArray = @[].mutableCopy;
    
    // 年
    NSInteger startYear = minDate.year;
    NSInteger endYear = maxDate.year;
    NSInteger yearIndex = 0;
    for (NSInteger i = startYear; i <= endYear; i++) {
        NSString *yearString = [NSString stringWithFormat:@"%ld", (long)i];
        SYQuickStartPickerItem *item = [SYQuickStartPickerItem new];
        item.index = yearIndex;
        item.text = yearString;
        [yearArray addObject:item];
        yearIndex ++;
    }
    
    // 月
    NSMutableArray *monthTrimArray = @[].mutableCopy;
    for (NSInteger i = 1; i <= 12; i++) {
        SYQuickStartPickerItem *item = [[SYQuickStartPickerItem alloc] init];
        item.index = i - 1;
        item.text = [NSString stringWithFormat:@"%ld", i];
        
        NSDate *tempDate = [NSDate dateWithYear:year month:item.text.integerValue day:1];
        NSDate *tempMinDate = [NSDate dateWithYear:minDate.year month:minDate.month day:1];
        NSDate *tempMaxDate = [NSDate dateWithYear:maxDate.year month:maxDate.month day:1];
        NSTimeInterval tempDateTI = [tempDate timeIntervalSince1970];
        NSTimeInterval minDateTI = [tempMinDate timeIntervalSince1970];
        NSTimeInterval maxDateTI = [tempMaxDate timeIntervalSince1970];
        if (tempDateTI >= minDateTI && tempDateTI <= maxDateTI) {
            [monthTrimArray addObject:item];
        }
    }
    monthArray = monthTrimArray.copy;
    
    // 日
    NSMutableArray *dayTrimArray = @[].mutableCopy;
    NSInteger dayCount = [NSDate daysInMonth:month ofYear:year];
    for (NSInteger i = 1; i <= dayCount; i++) {
        SYQuickStartPickerItem *item = [[SYQuickStartPickerItem alloc] init];
        item.index = i - 1;
        item.text = [NSString stringWithFormat:@"%ld", i];
        
        NSDate *tempDate = [NSDate dateWithYear:year month:month day:item.text.integerValue];
        NSDate *tempMinDate = [NSDate dateWithYear:minDate.year month:minDate.month day:minDate.day];
        NSDate *tempMaxDate = [NSDate dateWithYear:maxDate.year month:maxDate.month day:maxDate.day];
        NSTimeInterval tempDateTI = [tempDate timeIntervalSince1970];
        NSTimeInterval minDateTI = [tempMinDate timeIntervalSince1970];
        NSTimeInterval maxDateTI = [tempMaxDate timeIntervalSince1970];
        if (tempDateTI >= minDateTI && tempDateTI <= maxDateTI) {
            [dayTrimArray addObject:item];
        }
    }
    dayArray = dayTrimArray.copy;
    
    self.dataArray = @[yearArray, monthArray, dayArray];
}

#pragma mark 选中
/// 选中日期
- (NSArray *)setSelectRowsWithDate:(NSDate *)date {
    self.dateRetArray = @[].mutableCopy;
    
    NSMutableArray *array = [NSMutableArray array];
    for (NSArray *tempArray in self.dataArray) {
        [array addObject:@(tempArray.count - 1)];
    }
    if (self.pickerType == SYQuickStartPickerType_Date) {
        NSArray *indexesArray = [NSMutableArray new];
        NSTimeInterval dateTI = [date timeIntervalSince1970];
        NSDate *minDate = [self.data firstObject];
        NSDate *maxDate = [self.data lastObject];
        NSTimeInterval minDateTI = [minDate timeIntervalSince1970];
        NSTimeInterval maxDateTI = [maxDate timeIntervalSince1970];
        BOOL between = dateTI >= minDateTI && dateTI <= maxDateTI;
        if (between) {
            NSArray *dateArray = @[[NSString stringWithFormat:@"%ld", date.year], [NSString stringWithFormat:@"%ld", date.month], [NSString stringWithFormat:@"%ld", date.day]];
            //刷新dataArray
            [self loadDataArrayWithYear:date.year month:date.month];
            if (dateArray.count == array.count) {
                for (int i = 0; i < self.dataArray.count; i ++) {
                    NSArray *theData = self.dataArray[i];
                    for (int j = 0; j < theData.count; j ++) {
                        SYQuickStartPickerItem *item = theData[j];
                        if ([item.text isEqualToString:dateArray[i]]) {
                            array[i] = @(item.index);
                            [self.dateRetArray addObject:item]; // dateRetArray 选中
                            break;
                        }
                    }
                }
            }
        }
    }
    
    return array;
}

/// 字符串选中
- (NSArray *)setSelectRowsWithString:(NSString *)string {
    NSInteger index = 0;
    if (self.pickerType == SYQuickStartPickerType_Gender) {
        NSArray *theData = self.data.firstObject;
        for (int i = 0; i < theData.count; i ++) {
            if ([string isEqualToString:theData[i]]) {
                index = i;
                break;
            }
        }
    }
    
    SYQuickStartPickerItem *item = self.dataArray[0][index];
    
    return @[@(index)];
}

#pragma mark - UIPickerViewDelegate, UIPickerViewDataSource

- (CGFloat)pickerView:(UIPickerView *)pickerView rowHeightForComponent:(NSInteger)component {
    CGFloat height = self.rowHeightForComponent;
    if (iPhone5) {
        height = SCREEN_By375(height);
    }
    return height;
}

- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    return self.dataArray.count;
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    NSArray *componentArray = self.dataArray[component];
    return componentArray.count;
}

- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    NSArray *componentArray = self.dataArray[component];
    SYQuickStartPickerItem *item = [componentArray imy_objectAtIndex:row];
    NSString *rowString = item.text;
    
    if (self.pickerType == SYQuickStartPickerType_Date) {
        if (component == 0) {
            rowString = [rowString stringByAppendingString:IMYString(@"年")];
        }
        
        if (component == 1) {
            rowString = [rowString stringByAppendingString:IMYString(@"月")];
        }
        
        if (component == 2) {
            rowString = [rowString stringByAppendingString:IMYString(@"日")];
        }
    }
    
    return rowString;
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component {
    switch (self.pickerType) {
        case SYQuickStartPickerType_Gender: {
        }
            break;
        case SYQuickStartPickerType_Date: {
            [self handleDatePickerView:pickerView didSelectRow:row inComponent:component];
        }
            break;
            
        default:
            break;
    }
}

- (void)handleDatePickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component {
    NSDate *minDate = [self.data firstObject];
    NSDate *maxDate = [self.data lastObject];
    
    NSArray *yearArray = [self.dataArray objectAtIndex:0];
    NSArray *monthArray = [self.dataArray objectAtIndex:1];
    NSArray *dayArray = [self.dataArray objectAtIndex:2];
    
    NSInteger yearRow = [pickerView selectedRowInComponent:0];
    NSInteger monthRow = [pickerView selectedRowInComponent:1];
    NSInteger dayRow = [pickerView selectedRowInComponent:2];
    SYQuickStartPickerItem *yearItem = yearArray[yearRow];
    SYQuickStartPickerItem *monthItem = monthArray[monthRow];
    SYQuickStartPickerItem *dayItem = dayArray[dayRow];
    
    // 月
    NSMutableArray *monthTrimArray = @[].mutableCopy;
    for (NSInteger i = 1; i <= 12; i++) {
        SYQuickStartPickerItem *item = [[SYQuickStartPickerItem alloc] init];
        item.index = i - 1;
        item.text = [NSString stringWithFormat:@"%ld", i];
        
        NSDate *tempDate = [NSDate dateWithYear:yearItem.text.integerValue month:item.text.integerValue day:1];
        NSDate *tempMinDate = [NSDate dateWithYear:minDate.year month:minDate.month day:1];
        NSDate *tempMaxDate = [NSDate dateWithYear:maxDate.year month:maxDate.month day:1];
        NSTimeInterval tempDateTI = [tempDate timeIntervalSince1970];
        NSTimeInterval minDateTI = [tempMinDate timeIntervalSince1970];
        NSTimeInterval maxDateTI = [tempMaxDate timeIntervalSince1970];
        if (tempDateTI >= minDateTI && tempDateTI <= maxDateTI) {
            [monthTrimArray addObject:item];
        }
    }
    if (monthRow >= monthTrimArray.count) {
        monthRow = monthTrimArray.count - 1;
    }
    monthItem = monthTrimArray[monthRow];
    monthArray = monthTrimArray.copy;
    
    // 日
    NSMutableArray *dayTrimArray = @[].mutableCopy;
    NSInteger dayCount = [NSDate daysInMonth:monthItem.text.integerValue ofYear:yearItem.text.integerValue];
    for (NSInteger i = 1; i <= dayCount; i++) {
        SYQuickStartPickerItem *item = [[SYQuickStartPickerItem alloc] init];
        item.index = i - 1;
        item.text = [NSString stringWithFormat:@"%ld", i];
        
        NSDate *tempDate = [NSDate dateWithYear:yearItem.text.integerValue month:monthItem.text.integerValue day:item.text.integerValue];
        NSDate *tempMinDate = [NSDate dateWithYear:minDate.year month:minDate.month day:minDate.day];
        NSDate *tempMaxDate = [NSDate dateWithYear:maxDate.year month:maxDate.month day:maxDate.day];
        NSTimeInterval tempDateTI = [tempDate timeIntervalSince1970];
        NSTimeInterval minDateTI = [tempMinDate timeIntervalSince1970];
        NSTimeInterval maxDateTI = [tempMaxDate timeIntervalSince1970];
        if (tempDateTI >= minDateTI && tempDateTI <= maxDateTI) {
            [dayTrimArray addObject:item];
        }
    }
    if (dayRow >= dayTrimArray.count) {
        dayRow = dayTrimArray.count - 1;
    }
    dayItem = dayTrimArray[dayRow];
    dayArray = dayTrimArray.copy;
    
    self.dataArray = @[yearArray, monthArray, dayArray];
    
    [pickerView reloadAllComponents];
    
    self.dateRetArray = @[yearItem, monthItem, dayItem].mutableCopy;
    !self.didSelectDatePickerViewBlock?:self.didSelectDatePickerViewBlock();
}

- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(UIView *)view {
    UILabel *pickerLabel = (UILabel *)view;
    if (!pickerLabel) {
        pickerLabel = [[UILabel alloc] init];
        pickerLabel.adjustsFontSizeToFitWidth = YES;
        [pickerLabel setTextAlignment:NSTextAlignmentCenter];
        [pickerLabel setBackgroundColor:[UIColor clearColor]];
        CGFloat size = 20;
        if (iPhone5) {
            size = SCREEN_By375(size);
        }
        pickerLabel.font = [UIFont imy_MediumFontWith:size];
        [pickerLabel imy_setTextColor:kCK_Black_A];
    }
    pickerLabel.text = [self pickerView:pickerView titleForRow:row forComponent:component];
    return pickerLabel;
}

@end
