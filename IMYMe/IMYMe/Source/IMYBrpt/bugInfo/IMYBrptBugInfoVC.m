//
//  IMYBrptBugInfoVC.m
//  IMYMe
//
//  Created by huangbaoqin on 2025/5/13.
//

#ifdef DEBUG

#import "IMYBrptBugInfoVC.h"
#import "IMYBrptBugInfoVM.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYBrptBugInfoCell.h"
#import "IMYBrptBugInfoImagesCell.h"
#import "IMYBrptIterationSelectVC.h"
#import "IMYBrptUserSelectVC.h"
#import "IMYBrptStrSelectVC.h"
#import "IMYBrptManager.h"
#import "IMYBrptHomeWindow.h"

@interface IMYBrptBugInfoVC () <UITableViewDelegate, UITableViewDataSource, IMYAssetPickerControllerDelegate>

@property (nonatomic, strong) IMYBrptBugInfoVM *vm;

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) UIView *submitView;

@property (nonatomic, strong) IMYCapsuleButton *submitBtn;

@end

@implementation IMYBrptBugInfoVC

- (instancetype)init {
    self = [super init];
    if (self) {
        self.vm = [[IMYBrptBugInfoVM alloc] init];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    
    @weakify(self);
    [self.vm loadDatas:^{
        @strongify(self);
        imy_asyncMainBlock(^{
            @strongify(self);
            [self.tableView reloadData];
        });
    }];
}

// MARK: - setup

- (void)setupUI {
    self.navigationItem.title = @"创建 BUG";
    
    [self.view addSubview:self.submitView];
    [self.submitView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.view.mas_bottom).offset(0);
        make.leading.mas_equalTo(self.view.mas_leading).offset(0);
        make.trailing.mas_equalTo(self.view.mas_trailing).offset(0);
    }];
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view.mas_top).offset(0);
        make.leading.mas_equalTo(self.view.mas_leading).offset(0);
        make.trailing.mas_equalTo(self.view.mas_trailing).offset(0);
        make.bottom.mas_equalTo(self.view.mas_bottom).offset(0);
    }];

    [self.view addSubview:self.submitView];
    [self.submitView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.view.mas_bottom).offset(0);
        make.leading.mas_equalTo(self.view.mas_leading).offset(0);
        make.trailing.mas_equalTo(self.view.mas_trailing).offset(0);
    }];
}

// MARK: - actions

- (void)handleClickImageEvent:(id)sender {
    if ([IMYAssetsManager isAccessible] == NO) {
        [UIWindow imy_showTextHUDWithDelay:0.0 text:IMYString(@"相册照片无法显示啦，请在系统设置-隐私-照片中打开美柚开关")];
        return;
    }
    
    IMYAssetPickerController *vc = [[IMYAssetPickerController alloc] init];
    vc.allowsMultipleSelection = YES;
    vc.styleType = IMYAssetPickerUITypeNew;
    vc.allowSelectionVideo = NO;
    vc.maximumNumberOfSelection = 9;
    vc.minimumNumberOfSelection = 0;
    vc.isHiddenCamera = NO;
    vc.delegate = self;
    vc.selectedAssetArray = self.vm.currentBugInfoModel.assets.mutableCopy;
    
    [self imy_present:vc];
}

- (void)handleSubmitBtnEvent:(id)sender {
    if (imy_isEmptyString(self.vm.currentBugInfoModel.iterationModel.id)) {
        [UIWindow imy_showTextHUD:@"迭代为空，不能提交"];
        return;
    }
    
    if (imy_isEmptyString(self.vm.currentBugInfoModel.reporter)) {
        [UIWindow imy_showTextHUD:@"创建人为空，不能提交"];
        return;
    }
    
    if (imy_isEmptyString(self.vm.currentBugInfoModel.current_owner)) {
        [UIWindow imy_showTextHUD:@"处理人为空，不能提交"];
        return;
    }
    
    if (imy_isEmptyString(self.vm.currentBugInfoModel.de)) {
        [UIWindow imy_showTextHUD:@"开发人员为空，不能提交"];
        return;
    }
    
    [[IMYBrptHomeWindow sharedInstance] hide];
    [[IMYBrptManager sharedInstance] report:self.vm.currentBugInfoModel];
}

// MARK: - IMYAssetPickerControllerDelegate

- (void)assetPickerController:(IMYAssetPickerController *)assetPickerController didSelectAssets:(NSArray <IMYAssetModel *>*)assets {
    // assets
    self.vm.currentBugInfoModel.assets = assets;
    
    // images
    NSArray *images = [self.vm.currentBugInfoModel.assets bk_map:^id(IMYAssetModel *obj) {
        return obj.originImage;
    }];
    self.vm.currentBugInfoModel.images = images;
    
    // buginfoModel
    [self.vm changeCurrentBugInfoModel:self.vm.currentBugInfoModel];
    
    // reload
    [self.tableView reloadData];
}

- (void)assetPickerControllerWillCancelling:(IMYAssetPickerController *)assetPickerController {
    // assets
    self.vm.currentBugInfoModel.assets = assetPickerController.selectedAssetArray;
    
    // images
    NSArray *images = [self.vm.currentBugInfoModel.assets bk_map:^id(IMYAssetModel *obj) {
        return obj.originImage;
    }];
    self.vm.currentBugInfoModel.images = images;
    
    // buginfoModel
    [self.vm changeCurrentBugInfoModel:self.vm.currentBugInfoModel];
    
    // reload
    [self.tableView reloadData];
}

// MARK: - UITableViewDelegate, UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.vm.cellModels.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYBrptBugInfoCellModel *cellModel = self.vm.cellModels[indexPath.row];
    
    if (cellModel.cellType == IMYBrptBugInfoCellType_images) {
        static NSString *identification = @"IMYBrptBugInfoImagesCell";
        IMYBrptBugInfoImagesCell *cell = [tableView dequeueReusableCellWithIdentifier:identification forIndexPath:indexPath];
        
        if (!cell) {
            cell = [[IMYBrptBugInfoImagesCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identification];
            cell.backgroundColor = [UIColor clearColor];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
        }
        
        [cell setCellModel:cellModel];
        
        @weakify(self);
        [cell setDidSelctItem:^(IMYBrptBugInfoCellModel * _Nonnull cellModel, UIImage * _Nonnull image) {
            @strongify(self);
            [self handleClickImageEvent:nil];
        }];
        
        [cell setDidClickAddBtn:^{
            @strongify(self);
            [self handleClickImageEvent:nil];
        }];
        
        return cell;
    }
    
    static NSString *identification = @"IMYBrptBugInfoCell";
    IMYBrptBugInfoCell *cell = [tableView dequeueReusableCellWithIdentifier:identification forIndexPath:indexPath];
    
    if (!cell) {
        cell = [[IMYBrptBugInfoCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identification];
        cell.backgroundColor = [UIColor clearColor];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    
    [cell setCellModel:cellModel];

    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYBrptBugInfoCellModel *cellModel = self.vm.cellModels[indexPath.row];
    
    if (cellModel.cellType == IMYBrptBugInfoCellType_images) {
        return [IMYBrptBugInfoImagesCell cellHeight:cellModel];
    }
    
    return [IMYBrptBugInfoCell cellHeight:cellModel];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    IMYBrptBugInfoCellModel *cellModel = self.vm.cellModels[indexPath.row];
    
    switch (cellModel.cellType) {
        case IMYBrptBugInfoCellType_iteration: {
            IMYBrptIterationSelectVC *VC = [[IMYBrptIterationSelectVC alloc] initWithTitle:cellModel.title];
            @weakify(self);
            [VC setRetBlock:^(IMYBrptIterationModel * _Nullable model) {
                @strongify(self);
                self.vm.currentBugInfoModel.iterationModel = model;
                [self.vm changeCurrentBugInfoModel:self.vm.currentBugInfoModel];
                [self.tableView reloadData];
            }];
            [self imy_push:VC];
        }
            break;
            
        case IMYBrptBugInfoCellType_reporter: {
            IMYBrptUserSelectVC *userVC = [[IMYBrptUserSelectVC alloc] initWithTitle:cellModel.title];
            @weakify(self);
            [userVC setRetBlock:^(NSString * _Nullable name) {
                @strongify(self);
                self.vm.currentBugInfoModel.reporter = name;
                [self.vm changeCurrentBugInfoModel:self.vm.currentBugInfoModel];
                [self.tableView reloadData];
            }];
            [self imy_push:userVC];
        }
            break;
            
        case IMYBrptBugInfoCellType_current_owner: {
            IMYBrptUserSelectVC *userVC = [[IMYBrptUserSelectVC alloc] initWithTitle:cellModel.title];
            @weakify(self);
            [userVC setRetBlock:^(NSString * _Nullable name) {
                @strongify(self);
                self.vm.currentBugInfoModel.current_owner = name;
                [self.vm changeCurrentBugInfoModel:self.vm.currentBugInfoModel];
                [self.tableView reloadData];
            }];
            [self imy_push:userVC];
        }
            break;
            
        case IMYBrptBugInfoCellType_de: {
            IMYBrptUserSelectVC *userVC = [[IMYBrptUserSelectVC alloc] initWithTitle:cellModel.title];
            @weakify(self);
            [userVC setRetBlock:^(NSString * _Nullable name) {
                @strongify(self);
                self.vm.currentBugInfoModel.de = name;
                [self.vm changeCurrentBugInfoModel:self.vm.currentBugInfoModel];
                [self.tableView reloadData];
            }];
            [self imy_push:userVC];
        }
            break;
            
        case IMYBrptBugInfoCellType_priority: {
            NSArray *models = [IMYBrptStrModel priorityModels];
            IMYBrptStrSelectVC *strVC = [[IMYBrptStrSelectVC alloc] initWithModels:models title:cellModel.title];
            @weakify(self);
            [strVC setRetBlock:^(IMYBrptStrModel * _Nonnull strModel) {
                @strongify(self);
                self.vm.currentBugInfoModel.priority = strModel.str;
                self.vm.currentBugInfoModel.priorityDisplay = strModel.displayStr;
                [self.vm changeCurrentBugInfoModel:self.vm.currentBugInfoModel];
                [self.tableView reloadData];
            }];
            [self imy_push:strVC];
        }
            break;
            
        case IMYBrptBugInfoCellType_severity: {
            NSArray *models = [IMYBrptStrModel severityModels];
            IMYBrptStrSelectVC *strVC = [[IMYBrptStrSelectVC alloc] initWithModels:models title:cellModel.title];
            @weakify(self);
            [strVC setRetBlock:^(IMYBrptStrModel * _Nonnull strModel) {
                @strongify(self);
                self.vm.currentBugInfoModel.severity = strModel.str;
                self.vm.currentBugInfoModel.severityDisplay = strModel.displayStr;
                [self.vm changeCurrentBugInfoModel:self.vm.currentBugInfoModel];
                [self.tableView reloadData];
            }];
            [self imy_push:strVC];
        }
            break;
            
        case IMYBrptBugInfoCellType_images: {
            [self handleClickImageEvent:nil];
        }
            break;
            
        default:
            break;
    }
}

// MARK: - get

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = [UIColor clearColor];
        [_tableView registerClass:[IMYBrptBugInfoCell class] forCellReuseIdentifier:@"IMYBrptBugInfoCell"];
        [_tableView registerClass:[IMYBrptBugInfoImagesCell class] forCellReuseIdentifier:@"IMYBrptBugInfoImagesCell"];
    }
    return _tableView;
}

- (UIView *)submitView {
    if (!_submitView) {
        _submitView = [UIView new];
        [_submitView imy_setBackgroundColorForKey:kCK_White_AN];
        [_submitView addSubview:self.submitBtn];
        [self.submitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(_submitView.mas_top).mas_offset(12);
            make.leading.mas_equalTo(_submitView.mas_leading).offset(12);
            make.trailing.mas_equalTo(_submitView.mas_trailing).offset(-12);
            make.height.mas_offset(48);
            make.bottom.mas_equalTo(_submitView.mas_safeAreaLayoutGuideBottom).offset(-12);
        }];
        
        UIView *line = [UIView new];
        [line imy_setBackgroundColorForKey:kCK_Black_EN];
        [_submitView addSubview:line];
        [line mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(_submitView.mas_top).offset(0);
            make.leading.mas_equalTo(_submitView.mas_leading).offset(0);
            make.trailing.mas_equalTo(_submitView.mas_trailing).offset(0);
            make.height.mas_equalTo(0.5);
        }];
    }
    return _submitView;
}

- (IMYCapsuleButton *)submitBtn {
    if (!_submitBtn) {
        _submitBtn = [IMYCapsuleButton new];
        _submitBtn.type = IMYButtonTypeFillRed;
        _submitBtn.enabled = YES;
        [_submitBtn.titleLabel setFont:[UIFont systemFontOfSize:18]];
        [_submitBtn imy_setTitle:IMYString(@"提交") state:UIControlStateNormal];
        [_submitBtn addTarget:self action:@selector(handleSubmitBtnEvent:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _submitBtn;
}

@end

#endif
