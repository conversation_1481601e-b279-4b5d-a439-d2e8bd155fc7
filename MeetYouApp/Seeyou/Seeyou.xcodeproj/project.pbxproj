// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		03002D8625B03C5D00D5D2AB /* CloudKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 03002D8525B03C5D00D5D2AB /* CloudKit.framework */; };
		039FCF49248E658C00481DA4 /* PhotosUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 039FCF48248E658C00481DA4 /* PhotosUI.framework */; };
		0E0B9C142E49EC5B008BD3DB /* SleepActivityWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E0B9C122E49EC5A008BD3DB /* SleepActivityWidget.swift */; };
		0E0B9C152E49EC5B008BD3DB /* FMCountingActivityWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E0B9C112E49EC5A008BD3DB /* FMCountingActivityWidget.swift */; };
		0E0B9C162E49EC5B008BD3DB /* BreastMilkActivityWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E0B9C102E49EC5A008BD3DB /* BreastMilkActivityWidget.swift */; };
		0E4748552E4481500092E5B5 /* MediumVipWidget03Widget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E4748522E4481500092E5B5 /* MediumVipWidget03Widget.swift */; };
		0E4748562E4481500092E5B5 /* SmallVipWidget01Widget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E47484E2E4481500092E5B5 /* SmallVipWidget01Widget.swift */; };
		0E4748572E4481500092E5B5 /* SmallVipWidget02Widget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E4748502E4481500092E5B5 /* SmallVipWidget02Widget.swift */; };
		0E4748602E4481590092E5B5 /* IMYBaByFeed02WidgetAppItents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E47485B2E4481590092E5B5 /* IMYBaByFeed02WidgetAppItents.swift */; };
		0E4748612E4481590092E5B5 /* IMYBabyFeed02Widget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E47485A2E4481590092E5B5 /* IMYBabyFeed02Widget.swift */; };
		0E4748622E4481590092E5B5 /* IMYBabyFeed01Widget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E4748582E4481590092E5B5 /* IMYBabyFeed01Widget.swift */; };
		0E4748632E4481590092E5B5 /* IMYBabyFeed03Widget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0E47485D2E4481590092E5B5 /* IMYBabyFeed03Widget.swift */; };
		16450DAF240CEBFA00BDA3C0 /* AuthenticationServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 16450DAE240CEBFA00BDA3C0 /* AuthenticationServices.framework */; };
		16665D268C87ABD3FB229A98 /* libPods-Seeyou Today.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 1466F596ECC91FDDD943FB88 /* libPods-Seeyou Today.a */; };
		23535E1F7602E051DD2022D4 /* libPods-Seeyou.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6BBEF72B7BC04E6193AB80D3 /* libPods-Seeyou.a */; };
		2C1A56BB1A1AD14C005135AD /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8D0B5DB9192AF58D00BC443D /* CoreMotion.framework */; };
		2C1E73FF19EB97CB00870CF6 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2C1E73FE19EB97CB00870CF6 /* Accelerate.framework */; };
		2C9B715A1A36908200DF1AE2 /* LocalAuthentication.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2C9B71591A36908200DF1AE2 /* LocalAuthentication.framework */; };
		2CA58E5A19EE20DE00B369D5 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2CA58E5919EE20DE00B369D5 /* Images.xcassets */; };
		31821529EE5F54F5396488FF /* MapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 318219E03343363FD8C93B0F /* MapKit.framework */; };
		31821BB4613B845F58C509D8 /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 31821C4046377D022A838225 /* CoreLocation.framework */; };
		31821F65BB12D4FE731276A5 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 318216A4B9460EFC9E11FB51 /* AudioToolbox.framework */; };
		36039EC82151F2E4008BD5E5 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 36039EC72151F2E3008BD5E5 /* libc++.tbd */; };
		360AB8B02BC624A100C30079 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 360AB8AF2BC624A100C30079 /* PrivacyInfo.xcprivacy */; };
		360AB8B22BC628AC00C30079 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 360AB8B12BC628AC00C30079 /* PrivacyInfo.xcprivacy */; };
		360AB8B42BC628FB00C30079 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 360AB8B32BC628FB00C30079 /* PrivacyInfo.xcprivacy */; };
		360AB8B62BC6294600C30079 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 360AB8B52BC6294600C30079 /* PrivacyInfo.xcprivacy */; };
		364CF67E2AE2518B00E63DD4 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = A99640431FD69E530002A5E2 /* libz.tbd */; };
		364CF6812AE2525100E63DD4 /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = A99640401FD69DC90002A5E2 /* libsqlite3.tbd */; };
		364CF6822AE2526600E63DD4 /* WatchConnectivity.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5092F8B324553A6800249707 /* WatchConnectivity.framework */; };
		36580F53237D418800A8F66D /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36580F52237D418700A8F66D /* StoreKit.framework */; };
		3660D6BE2AE25AB600DB55C2 /* libxml2.2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 364CF67F2AE2520F00E63DD4 /* libxml2.2.tbd */; };
		3660D6BF2AE25AC100DB55C2 /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 364CF6802AE2524A00E63DD4 /* libiconv.tbd */; };
		3660D6C02AE25AC700DB55C2 /* libicucore.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 364CF67D2AE2517F00E63DD4 /* libicucore.tbd */; };
		3667CB792DAD184D00920617 /* libswiftCoreGraphics.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 3667CB772DAD17B000920617 /* libswiftCoreGraphics.tbd */; settings = {ATTRIBUTES = (Weak, ); }; };
		3670BF232E02ABFD00239551 /* IMYSharedExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3670BF1C2E02ABFC00239551 /* IMYSharedExtension.swift */; };
		3670BF242E02ABFD00239551 /* ShareViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3670BF212E02ABFC00239551 /* ShareViewController.swift */; };
		3670BF262E02ABFD00239551 /* MainInterface.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 3670BF1F2E02ABFC00239551 /* MainInterface.storyboard */; };
		3670BF282E02AC7100239551 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 3670BF272E02AC7100239551 /* PrivacyInfo.xcprivacy */; };
		3673C5912DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5872DBDDF2000966021 /* <EMAIL> */; };
		3673C5922DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C54E2DBDDF2000966021 /* <EMAIL> */; };
		3673C5932DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5622DBDDF2000966021 /* <EMAIL> */; };
		3673C5942DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C52D2DBDDF2000966021 /* <EMAIL> */; };
		3673C5952DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5342DBDDF2000966021 /* <EMAIL> */; };
		3673C5962DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5392DBDDF2000966021 /* <EMAIL> */; };
		3673C5972DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5552DBDDF2000966021 /* <EMAIL> */; };
		3673C5982DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5282DBDDF2000966021 /* <EMAIL> */; };
		3673C5992DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C55E2DBDDF2000966021 /* <EMAIL> */; };
		3673C59A2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5532DBDDF2000966021 /* <EMAIL> */; };
		3673C59B2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5482DBDDF2000966021 /* <EMAIL> */; };
		3673C59C2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C56A2DBDDF2000966021 /* <EMAIL> */; };
		3673C59D2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5682DBDDF2000966021 /* <EMAIL> */; };
		3673C59E2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5742DBDDF2000966021 /* <EMAIL> */; };
		3673C59F2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5582DBDDF2000966021 /* <EMAIL> */; };
		3673C5A02DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C56D2DBDDF2000966021 /* <EMAIL> */; };
		3673C5A12DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5712DBDDF2000966021 /* <EMAIL> */; };
		3673C5A22DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C56B2DBDDF2000966021 /* <EMAIL> */; };
		3673C5A32DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C56C2DBDDF2000966021 /* <EMAIL> */; };
		3673C5A42DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C52B2DBDDF2000966021 /* <EMAIL> */; };
		3673C5A52DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C58E2DBDDF2000966021 /* <EMAIL> */; };
		3673C5A62DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5212DBDDF2000966021 /* <EMAIL> */; };
		3673C5A72DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C57A2DBDDF2000966021 /* <EMAIL> */; };
		3673C5A82DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5812DBDDF2000966021 /* <EMAIL> */; };
		3673C5A92DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5752DBDDF2000966021 /* <EMAIL> */; };
		3673C5AA2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C52E2DBDDF2000966021 /* <EMAIL> */; };
		3673C5AB2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C52F2DBDDF2000966021 /* <EMAIL> */; };
		3673C5AC2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5322DBDDF2000966021 /* <EMAIL> */; };
		3673C5AD2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C53B2DBDDF2000966021 /* <EMAIL> */; };
		3673C5AE2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5542DBDDF2000966021 /* <EMAIL> */; };
		3673C5AF2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5472DBDDF2000966021 /* <EMAIL> */; };
		3673C5B02DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5662DBDDF2000966021 /* <EMAIL> */; };
		3673C5B12DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C54B2DBDDF2000966021 /* <EMAIL> */; };
		3673C5B22DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5272DBDDF2000966021 /* <EMAIL> */; };
		3673C5B32DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C55C2DBDDF2000966021 /* <EMAIL> */; };
		3673C5B42DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5292DBDDF2000966021 /* <EMAIL> */; };
		3673C5B52DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5762DBDDF2000966021 /* <EMAIL> */; };
		3673C5B62DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5262DBDDF2000966021 /* <EMAIL> */; };
		3673C5B72DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5232DBDDF2000966021 /* <EMAIL> */; };
		3673C5B82DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C53A2DBDDF2000966021 /* <EMAIL> */; };
		3673C5B92DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C55B2DBDDF2000966021 /* <EMAIL> */; };
		3673C5BA2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5612DBDDF2000966021 /* <EMAIL> */; };
		3673C5BB2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C57C2DBDDF2000966021 /* <EMAIL> */; };
		3673C5BC2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5412DBDDF2000966021 /* <EMAIL> */; };
		3673C5BD2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C58B2DBDDF2000966021 /* <EMAIL> */; };
		3673C5BE2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C57D2DBDDF2000966021 /* <EMAIL> */; };
		3673C5BF2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C53C2DBDDF2000966021 /* <EMAIL> */; };
		3673C5C02DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5632DBDDF2000966021 /* <EMAIL> */; };
		3673C5C12DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5332DBDDF2000966021 /* <EMAIL> */; };
		3673C5C22DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C55A2DBDDF2000966021 /* <EMAIL> */; };
		3673C5C32DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5652DBDDF2000966021 /* <EMAIL> */; };
		3673C5C42DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C55D2DBDDF2000966021 /* <EMAIL> */; };
		3673C5C52DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5442DBDDF2000966021 /* <EMAIL> */; };
		3673C5C62DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5782DBDDF2000966021 /* <EMAIL> */; };
		3673C5C72DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C57B2DBDDF2000966021 /* <EMAIL> */; };
		3673C5C82DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5722DBDDF2000966021 /* <EMAIL> */; };
		3673C5C92DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5222DBDDF2000966021 /* <EMAIL> */; };
		3673C5CA2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5822DBDDF2000966021 /* <EMAIL> */; };
		3673C5CB2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C54D2DBDDF2000966021 /* <EMAIL> */; };
		3673C5CC2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C54F2DBDDF2000966021 /* <EMAIL> */; };
		3673C5CD2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5732DBDDF2000966021 /* <EMAIL> */; };
		3673C5CE2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5642DBDDF2000966021 /* <EMAIL> */; };
		3673C5CF2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C56F2DBDDF2000966021 /* <EMAIL> */; };
		3673C5D02DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5562DBDDF2000966021 /* <EMAIL> */; };
		3673C5D12DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C56E2DBDDF2000966021 /* <EMAIL> */; };
		3673C5D22DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C58F2DBDDF2000966021 /* <EMAIL> */; };
		3673C5D32DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5362DBDDF2000966021 /* <EMAIL> */; };
		3673C5D42DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5592DBDDF2000966021 /* <EMAIL> */; };
		3673C5D52DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5832DBDDF2000966021 /* <EMAIL> */; };
		3673C5D62DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5372DBDDF2000966021 /* <EMAIL> */; };
		3673C5D72DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C53F2DBDDF2000966021 /* <EMAIL> */; };
		3673C5D82DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C57F2DBDDF2000966021 /* <EMAIL> */; };
		3673C5D92DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5852DBDDF2000966021 /* <EMAIL> */; };
		3673C5DA2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C54A2DBDDF2000966021 /* <EMAIL> */; };
		3673C5DB2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5242DBDDF2000966021 /* <EMAIL> */; };
		3673C5DC2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5892DBDDF2000966021 /* <EMAIL> */; };
		3673C5DD2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5522DBDDF2000966021 /* <EMAIL> */; };
		3673C5DE2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C52A2DBDDF2000966021 /* <EMAIL> */; };
		3673C5DF2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C54C2DBDDF2000966021 /* <EMAIL> */; };
		3673C5E02DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C53E2DBDDF2000966021 /* <EMAIL> */; };
		3673C5E12DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5672DBDDF2000966021 /* <EMAIL> */; };
		3673C5E22DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5382DBDDF2000966021 /* <EMAIL> */; };
		3673C5E32DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5462DBDDF2000966021 /* <EMAIL> */; };
		3673C5E42DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5432DBDDF2000966021 /* <EMAIL> */; };
		3673C5E52DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5422DBDDF2000966021 /* <EMAIL> */; };
		3673C5E62DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C53D2DBDDF2000966021 /* <EMAIL> */; };
		3673C5E72DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5312DBDDF2000966021 /* <EMAIL> */; };
		3673C5E82DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5252DBDDF2000966021 /* <EMAIL> */; };
		3673C5E92DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5452DBDDF2000966021 /* <EMAIL> */; };
		3673C5EA2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5492DBDDF2000966021 /* <EMAIL> */; };
		3673C5EB2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C58A2DBDDF2000966021 /* <EMAIL> */; };
		3673C5EC2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5772DBDDF2000966021 /* <EMAIL> */; };
		3673C5ED2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5882DBDDF2000966021 /* <EMAIL> */; };
		3673C5EE2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5352DBDDF2000966021 /* <EMAIL> */; };
		3673C5EF2DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C52C2DBDDF2000966021 /* <EMAIL> */; };
		3673C5F02DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5792DBDDF2000966021 /* <EMAIL> */; };
		3673C5F12DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5842DBDDF2000966021 /* <EMAIL> */; };
		3673C5F22DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5512DBDDF2000966021 /* <EMAIL> */; };
		3673C5F32DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C58C2DBDDF2000966021 /* <EMAIL> */; };
		3673C5F42DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C57E2DBDDF2000966021 /* <EMAIL> */; };
		3673C5F52DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C55F2DBDDF2000966021 /* <EMAIL> */; };
		3673C5F62DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5572DBDDF2000966021 /* <EMAIL> */; };
		3673C5F72DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5862DBDDF2000966021 /* <EMAIL> */; };
		3673C5F82DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C58D2DBDDF2000966021 /* <EMAIL> */; };
		3673C5F92DBDDF2000966021 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3673C5692DBDDF2000966021 /* <EMAIL> */; };
		36CBC2502C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC2412C23EDCB004D3030 /* <EMAIL> */; };
		36CBC2512C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC2422C23EDCB004D3030 /* <EMAIL> */; };
		36CBC2522C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC2432C23EDCB004D3030 /* <EMAIL> */; };
		36CBC2532C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC2442C23EDCB004D3030 /* <EMAIL> */; };
		36CBC2542C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC2452C23EDCB004D3030 /* <EMAIL> */; };
		36CBC2552C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC2462C23EDCB004D3030 /* <EMAIL> */; };
		36CBC2562C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC2472C23EDCB004D3030 /* <EMAIL> */; };
		36CBC2572C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC2482C23EDCB004D3030 /* <EMAIL> */; };
		36CBC2582C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC2492C23EDCB004D3030 /* <EMAIL> */; };
		36CBC2592C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC24A2C23EDCB004D3030 /* <EMAIL> */; };
		36CBC25A2C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC24B2C23EDCB004D3030 /* <EMAIL> */; };
		36CBC25B2C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC24C2C23EDCB004D3030 /* <EMAIL> */; };
		36CBC25C2C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC24D2C23EDCB004D3030 /* <EMAIL> */; };
		36CBC25D2C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC24E2C23EDCB004D3030 /* <EMAIL> */; };
		36CBC25E2C23EDCB004D3030 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36CBC24F2C23EDCB004D3030 /* <EMAIL> */; };
		36D177152D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D1770E2D3609E40072042E /* <EMAIL> */; };
		36D177162D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D177132D3609E40072042E /* <EMAIL> */; };
		36D177172D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D177052D3609E40072042E /* <EMAIL> */; };
		36D177182D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D1770B2D3609E40072042E /* <EMAIL> */; };
		36D177192D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D1770D2D3609E40072042E /* <EMAIL> */; };
		36D1771A2D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D1770A2D3609E40072042E /* <EMAIL> */; };
		36D1771B2D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D177102D3609E40072042E /* <EMAIL> */; };
		36D1771C2D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D1770F2D3609E40072042E /* <EMAIL> */; };
		36D1771D2D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D177072D3609E40072042E /* <EMAIL> */; };
		36D1771E2D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D177122D3609E40072042E /* <EMAIL> */; };
		36D1771F2D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D177112D3609E40072042E /* <EMAIL> */; };
		36D177202D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D177092D3609E40072042E /* <EMAIL> */; };
		36D177212D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D177062D3609E40072042E /* <EMAIL> */; };
		36D177222D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D177082D3609E40072042E /* <EMAIL> */; };
		36D177232D3609E40072042E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 36D1770C2D3609E40072042E /* <EMAIL> */; };
		36D384462D6D58EB0022C8C8 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 36D384452D6D58EB0022C8C8 /* PrivacyInfo.xcprivacy */; };
		36D6FE792BC66414005C059E /* LaunchScreenNew.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 36D6FE782BC66414005C059E /* LaunchScreenNew.storyboard */; };
		36F224F11D05302500604259 /* SeeyouTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 36F224F01D05302500604259 /* SeeyouTests.m */; };
		36FB1FA8287539D3006BADB6 /* NSBundle+MYAPPINFO.m in Sources */ = {isa = PBXBuildFile; fileRef = 36FB1FA7287539D3006BADB6 /* NSBundle+MYAPPINFO.m */; };
		3C1F62991DF02A3A00D082B1 /* IMYTodayWidgetNetworkHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C1F62981DF02A3A00D082B1 /* IMYTodayWidgetNetworkHelper.m */; };
		3C2A202C1DE691BB0090DA04 /* IMYTodayWidgetHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C2A202B1DE691BB0090DA04 /* IMYTodayWidgetHelper.m */; };
		3C2A202F1DE6920B0090DA04 /* IMYTodayArticleCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C2A202E1DE6920B0090DA04 /* IMYTodayArticleCell.m */; };
		3C2A20321DE692890090DA04 /* IMYTodayBottomCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C2A20311DE692890090DA04 /* IMYTodayBottomCell.m */; };
		3C2A20351DE698380090DA04 /* IMYTodayTopCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C2A20341DE698380090DA04 /* IMYTodayTopCell.m */; };
		3C32BAC61DE544F000C7F653 /* today-widget_advise.png in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BABA1DE544F000C7F653 /* today-widget_advise.png */; };
		3C32BAC71DE544F000C7F653 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BABB1DE544F000C7F653 /* <EMAIL> */; };
		3C32BAC81DE544F000C7F653 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BABC1DE544F000C7F653 /* <EMAIL> */; };
		3C32BAC91DE544F000C7F653 /* today-widget_record.png in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BABD1DE544F000C7F653 /* today-widget_record.png */; };
		3C32BACA1DE544F000C7F653 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BABE1DE544F000C7F653 /* <EMAIL> */; };
		3C32BACB1DE544F000C7F653 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BABF1DE544F000C7F653 /* <EMAIL> */; };
		3C32BACC1DE544F000C7F653 /* today-widget_search.png in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BAC01DE544F000C7F653 /* today-widget_search.png */; };
		3C32BACD1DE544F000C7F653 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BAC11DE544F000C7F653 /* <EMAIL> */; };
		3C32BACE1DE544F000C7F653 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BAC21DE544F000C7F653 /* <EMAIL> */; };
		3C32BACF1DE544F000C7F653 /* today-widget_sign.png in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BAC31DE544F000C7F653 /* today-widget_sign.png */; };
		3C32BAD01DE544F000C7F653 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BAC41DE544F000C7F653 /* <EMAIL> */; };
		3C32BAD11DE544F000C7F653 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3C32BAC51DE544F000C7F653 /* <EMAIL> */; };
		3C6EF1061DF062BA009FF953 /* NSString+IMYTodayWidget.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C6EF1051DF062BA009FF953 /* NSString+IMYTodayWidget.m */; };
		3C6EF1091DF06337009FF953 /* NSObject+IMYTodayWidget.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C6EF1081DF06337009FF953 /* NSObject+IMYTodayWidget.m */; };
		3C6EF10C1DF0653A009FF953 /* NSData+IMYTodayWidget.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C6EF10B1DF0653A009FF953 /* NSData+IMYTodayWidget.m */; };
		3CCC6AD61DEC0A3400DFAA6E /* SYSpotlightTest.m in Sources */ = {isa = PBXBuildFile; fileRef = 3CCC6AD51DEC0A3400DFAA6E /* SYSpotlightTest.m */; };
		3CD7D61C1DE2F2E70035CECA /* NotificationCenter.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2C5C9C9519F6448B004C2C4A /* NotificationCenter.framework */; };
		3CD7D6201DE2F2E70035CECA /* TodayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 3CD7D61F1DE2F2E70035CECA /* TodayViewController.m */; };
		3CD7D6231DE2F2E70035CECA /* MainInterface.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 3CD7D6211DE2F2E70035CECA /* MainInterface.storyboard */; };
		3CD7D6271DE2F2E70035CECA /* Seeyou Today.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 3CD7D61B1DE2F2E60035CECA /* Seeyou Today.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		3D71BF712283EF92008855AF /* HealthKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3D71BF702283EF92008855AF /* HealthKit.framework */; };
		5031E9782317BF490012E4B1 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5031E9772317BF490012E4B1 /* UserNotifications.framework */; };
		5031E9792317BF500012E4B1 /* NotificationCenter.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2C5C9C9519F6448B004C2C4A /* NotificationCenter.framework */; };
		5031E97B2317BF640012E4B1 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 5031E97A2317BF640012E4B1 /* libresolv.tbd */; };
		5031E97D2317BF6C0012E4B1 /* libSystem.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 5031E97C2317BF6C0012E4B1 /* libSystem.tbd */; };
		5031E97F2317BF780012E4B1 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5031E97E2317BF780012E4B1 /* CFNetwork.framework */; };
		5031E9812317BF830012E4B1 /* CoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5031E9802317BF830012E4B1 /* CoreServices.framework */; };
		5031E9832317BF9F0012E4B1 /* libobjc.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 5031E9822317BF9F0012E4B1 /* libobjc.tbd */; };
		5031E9872317BFBD0012E4B1 /* libobjc.A.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 5031E9862317BFBD0012E4B1 /* libobjc.A.tbd */; };
		5041C3E32CDD980E00505DC2 /* ExtensionDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5041C3E22CDD980E00505DC2 /* ExtensionDelegate.swift */; };
		5092F8B224553A5F00249707 /* CoreSpotlight.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5092F8B124553A5F00249707 /* CoreSpotlight.framework */; };
		532BAE8616FD801000321CFD /* ImageIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 532BAE8516FD801000321CFD /* ImageIO.framework */; };
		532BAE8816FD802500321CFD /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 532BAE8716FD802500321CFD /* MobileCoreServices.framework */; };
		539D0A5D16FC08030092F222 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 539D0A5C16FC08030092F222 /* UIKit.framework */; };
		539D0A5F16FC08030092F222 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 539D0A5E16FC08030092F222 /* Foundation.framework */; };
		539D0A6116FC08030092F222 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 539D0A6016FC08030092F222 /* CoreGraphics.framework */; };
		539D0B0B16FC2A120092F222 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 539D0B0A16FC2A120092F222 /* CoreTelephony.framework */; };
		53F48454174C5703001EE813 /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 53F48453174C5703001EE813 /* AdSupport.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		8D06516F2DFAAC6900271766 /* SeeyouShare.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 8D0651652DFAAC6800271766 /* SeeyouShare.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		8D21CB462CD4A3F300069EDC /* SeeyouWatch Watch App.app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = 8D21CB392CD4A3F100069EDC /* SeeyouWatch Watch App.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		8D240C082E49998500AA843A /* IMYContractionsWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D240C072E49998500AA843A /* IMYContractionsWidget.swift */; };
		8D3361052B624D1600FA162D /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8D3361042B624D1600FA162D /* WidgetKit.framework */; };
		8D3361072B624D1600FA162D /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8D3361062B624D1600FA162D /* SwiftUI.framework */; };
		8D33610A2B624D1700FA162D /* SeeyouWidgetBundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D3361092B624D1700FA162D /* SeeyouWidgetBundle.swift */; };
		8D33610E2B624D1700FA162D /* SeeyouWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D33610D2B624D1700FA162D /* SeeyouWidget.swift */; };
		8D3361112B624D1900FA162D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8D3361102B624D1900FA162D /* Assets.xcassets */; };
		8D3361172B624D1900FA162D /* SeeyouWidgetExtension.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 8D3361032B624D1600FA162D /* SeeyouWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		8D3361C72B637D3100FA162D /* SmallFreeWidget01Widget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D3361C12B637D3100FA162D /* SmallFreeWidget01Widget.swift */; };
		8D49717E2CDC480800D1EF0B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 8D49717D2CDC480800D1EF0B /* PrivacyInfo.xcprivacy */; };
		8D83EAB71AB91F6600CBE894 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8D83EAB61AB91F6600CBE894 /* Security.framework */; };
		8D8517C52B999247004D7383 /* BabyPhotoVipWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D8517C32B999246004D7383 /* BabyPhotoVipWidget.swift */; };
		8D8D7D9D2D559ECD00315CCF /* Intents.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8D8D7D9C2D559ECD00315CCF /* Intents.framework */; };
		8D8D7DA42D559ECD00315CCF /* SeeyouIntents.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = 8D8D7D9B2D559ECD00315CCF /* SeeyouIntents.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		8D8D7DAD2D559ED500315CCF /* IntentHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D8D7DAA2D559ED500315CCF /* IntentHandler.swift */; };
		8DA56DC62D65943900614D75 /* IMYTimelineProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D94C5742D6582AD008658C7 /* IMYTimelineProvider.swift */; };
		8DA56DD12D65C92700614D75 /* IMYIntentTimelineProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DA56DD02D65C92700614D75 /* IMYIntentTimelineProvider.swift */; };
		8DBD2C802E4C20D000F174CC /* IMYFMCountWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DBD2C7F2E4C20D000F174CC /* IMYFMCountWidget.swift */; };
		8DBD2C852E4C2D6C00F174CC /* IMYWidgetType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DBD2C842E4C2D6C00F174CC /* IMYWidgetType.swift */; };
		8DBD2C862E4C2D6C00F174CC /* IMYWidgetType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DBD2C842E4C2D6C00F174CC /* IMYWidgetType.swift */; };
		8DC803762D55B5F8004D205E /* Intents.intentdefinition in Sources */ = {isa = PBXBuildFile; fileRef = 8DC803752D55B5F8004D205E /* Intents.intentdefinition */; };
		8DC803772D55B5F8004D205E /* Intents.intentdefinition in Sources */ = {isa = PBXBuildFile; fileRef = 8DC803752D55B5F8004D205E /* Intents.intentdefinition */; };
		8DCBF8C62CD89A44008A510F /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DCBF8B82CD89A44008A510F /* ContentView.swift */; };
		8DCBF8C72CD89A44008A510F /* SeeyouWatchApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DCBF8B92CD89A44008A510F /* SeeyouWatchApp.swift */; };
		8DCBF8C82CD89A44008A510F /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8DCBF8B52CD89A44008A510F /* Preview Assets.xcassets */; };
		8DCBF8C92CD89A44008A510F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8DCBF8B72CD89A44008A510F /* Assets.xcassets */; };
		8DFFD65F2ADF6D5500FDCF9A /* SafariServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8DFFD65E2ADF6D5500FDCF9A /* SafariServices.framework */; };
		8DFFD6602ADF6D5C00FDCF9A /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2C1A56BC1A1AF504005135AD /* Photos.framework */; };
		9823FDBF3AD0423FEEF6F666 /* libPods-SeeyouWatch Watch App.a in Frameworks */ = {isa = PBXBuildFile; fileRef = D3A4B62C47A9EA009422F6B9 /* libPods-SeeyouWatch Watch App.a */; };
		A22E297B1919E3F900867C47 /* AddressBook.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A22E297A1919E3F900867C47 /* AddressBook.framework */; };
		A9957BDC1F04D81000934FF6 /* NotificationService.m in Sources */ = {isa = PBXBuildFile; fileRef = A9957BDB1F04D81000934FF6 /* NotificationService.m */; };
		A9957BE01F04D81000934FF6 /* Seeyou ServiceExtension.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = A9957BD81F04D81000934FF6 /* Seeyou ServiceExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		ACB86C5E21AD3EE100CF436B /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 539D0B0A16FC2A120092F222 /* CoreTelephony.framework */; };
		ACB86C5F21AD3EE500CF436B /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = A99640431FD69E530002A5E2 /* libz.tbd */; };
		ACB86C6721AD3F0500CF436B /* AVKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ACB86C6621AD3F0500CF436B /* AVKit.framework */; };
		C5F233E91F15F7BC00817CD1 /* NSDate+IMYTodayWidget.m in Sources */ = {isa = PBXBuildFile; fileRef = C5F233E81F15F7BC00817CD1 /* NSDate+IMYTodayWidget.m */; };
		CB2247CB16FC26E600D50AC1 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CB2247CA16FC26E600D50AC1 /* QuartzCore.framework */; };
		E9424F58177A5F60249DFFF5 /* libPods-SeeyouWidgetExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FEF8341F5D4676C4F76A4AF2 /* libPods-SeeyouWidgetExtension.a */; };
		ED31CFD524FF6C44001ED2E4 /* SYTestViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ED31CFD424FF6C44001ED2E4 /* SYTestViewController.swift */; };
		F55C9D5D1758A789000059B9 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F55C9D5C1758A789000059B9 /* AVFoundation.framework */; };
		F55C9D5F1758A799000059B9 /* MediaPlayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F55C9D5E1758A799000059B9 /* MediaPlayer.framework */; };
		F55C9D611758A7B7000059B9 /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F55C9D601758A7B7000059B9 /* AssetsLibrary.framework */; };
		F55C9D631758A7F3000059B9 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F55C9D621758A7F3000059B9 /* CoreMedia.framework */; };
		F597B3AC16FF3A7200394113 /* MessageUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F597B3AB16FF3A7200394113 /* MessageUI.framework */; };
		F5A2936F1831C9E20031E803 /* CoreImage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F5A2936E1831C9E20031E803 /* CoreImage.framework */; };
		F5A5FB3517276F5700018E46 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F5A5FB3417276F5700018E46 /* SystemConfiguration.framework */; };
		F5C5801517139DD800CA2312 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = F5C57FA517139DD700CA2312 /* main.m */; };
		FCBF21454AEFEFBD7247FE5A /* libPods-SeeyouIntents.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 719237411A016B70FF4EEE51 /* libPods-SeeyouIntents.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		362AC6F81D0522EF00B14848 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 539D0A5116FC08030092F222 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 539D0A5816FC08030092F222;
			remoteInfo = Seeyou;
		};
		3CD7D6251DE2F2E70035CECA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 539D0A5116FC08030092F222 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3CD7D61A1DE2F2E60035CECA;
			remoteInfo = "Seeyou Today";
		};
		8D06516D2DFAAC6900271766 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 539D0A5116FC08030092F222 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8D0651642DFAAC6800271766;
			remoteInfo = SeeyouShare;
		};
		8D21CB442CD4A3F300069EDC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 539D0A5116FC08030092F222 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8D21CB382CD4A3F100069EDC;
			remoteInfo = "SeeyouWatch Watch App";
		};
		8D3361152B624D1900FA162D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 539D0A5116FC08030092F222 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8D3361022B624D1500FA162D;
			remoteInfo = SeeyouWidgetExtension;
		};
		8D8D7DA22D559ECD00315CCF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 539D0A5116FC08030092F222 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8D8D7D9A2D559ECD00315CCF;
			remoteInfo = SeeyouIntents;
		};
		A9957BDE1F04D81000934FF6 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 539D0A5116FC08030092F222 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A9957BD71F04D81000934FF6;
			remoteInfo = "Seeyou ServiceExtension";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		03FEE06C1DC8B795008FF235 /* Embed Watch Content */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				8D21CB462CD4A3F300069EDC /* SeeyouWatch Watch App.app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
		A12C40331B7F42A90023323F /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				8D06516F2DFAAC6900271766 /* SeeyouShare.appex in Embed App Extensions */,
				3CD7D6271DE2F2E70035CECA /* Seeyou Today.appex in Embed App Extensions */,
				8D3361172B624D1900FA162D /* SeeyouWidgetExtension.appex in Embed App Extensions */,
				8D8D7DA42D559ECD00315CCF /* SeeyouIntents.appex in Embed App Extensions */,
				A9957BE01F04D81000934FF6 /* Seeyou ServiceExtension.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		03002D8525B03C5D00D5D2AB /* CloudKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CloudKit.framework; path = System/Library/Frameworks/CloudKit.framework; sourceTree = SDKROOT; };
		039FCF48248E658C00481DA4 /* PhotosUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = PhotosUI.framework; path = System/Library/Frameworks/PhotosUI.framework; sourceTree = SDKROOT; };
		0E0B9C102E49EC5A008BD3DB /* BreastMilkActivityWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BreastMilkActivityWidget.swift; sourceTree = "<group>"; };
		0E0B9C112E49EC5A008BD3DB /* FMCountingActivityWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FMCountingActivityWidget.swift; sourceTree = "<group>"; };
		0E0B9C122E49EC5A008BD3DB /* SleepActivityWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SleepActivityWidget.swift; sourceTree = "<group>"; };
		0E47484E2E4481500092E5B5 /* SmallVipWidget01Widget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmallVipWidget01Widget.swift; sourceTree = "<group>"; };
		0E4748502E4481500092E5B5 /* SmallVipWidget02Widget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmallVipWidget02Widget.swift; sourceTree = "<group>"; };
		0E4748522E4481500092E5B5 /* MediumVipWidget03Widget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MediumVipWidget03Widget.swift; sourceTree = "<group>"; };
		0E4748582E4481590092E5B5 /* IMYBabyFeed01Widget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYBabyFeed01Widget.swift; sourceTree = "<group>"; };
		0E47485A2E4481590092E5B5 /* IMYBabyFeed02Widget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYBabyFeed02Widget.swift; sourceTree = "<group>"; };
		0E47485B2E4481590092E5B5 /* IMYBaByFeed02WidgetAppItents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYBaByFeed02WidgetAppItents.swift; sourceTree = "<group>"; };
		0E47485D2E4481590092E5B5 /* IMYBabyFeed03Widget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYBabyFeed03Widget.swift; sourceTree = "<group>"; };
		1466F596ECC91FDDD943FB88 /* libPods-Seeyou Today.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Seeyou Today.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		16450DAE240CEBFA00BDA3C0 /* AuthenticationServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AuthenticationServices.framework; path = System/Library/Frameworks/AuthenticationServices.framework; sourceTree = SDKROOT; };
		1DC825B878CA0EF815FE7A92 /* Pods-Seeyou.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Seeyou.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-Seeyou/Pods-Seeyou.debug.xcconfig"; sourceTree = "<group>"; };
		25389EDDBC1F5D4523833381 /* Pods-Seeyou.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Seeyou.release.xcconfig"; path = "../Pods/Target Support Files/Pods-Seeyou/Pods-Seeyou.release.xcconfig"; sourceTree = "<group>"; };
		2C1A56BC1A1AF504005135AD /* Photos.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Photos.framework; path = System/Library/Frameworks/Photos.framework; sourceTree = SDKROOT; };
		2C1E73FE19EB97CB00870CF6 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		2C4B5A331A1052DA00D3A13E /* Seeyou.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = Seeyou.entitlements; sourceTree = "<group>"; };
		2C5C9C9519F6448B004C2C4A /* NotificationCenter.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = NotificationCenter.framework; path = System/Library/Frameworks/NotificationCenter.framework; sourceTree = SDKROOT; };
		2C9B71591A36908200DF1AE2 /* LocalAuthentication.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = LocalAuthentication.framework; path = System/Library/Frameworks/LocalAuthentication.framework; sourceTree = SDKROOT; };
		2CA58E5919EE20DE00B369D5 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		318216A4B9460EFC9E11FB51 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		318219E03343363FD8C93B0F /* MapKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MapKit.framework; path = System/Library/Frameworks/MapKit.framework; sourceTree = SDKROOT; };
		31821C4046377D022A838225 /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		3600E8F92CE1F27900CD2D1E /* SeeyouWatchApp.entitlements */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.entitlements; path = SeeyouWatchApp.entitlements; sourceTree = "<group>"; };
		36039EC72151F2E3008BD5E5 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		360AB8AF2BC624A100C30079 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		360AB8B12BC628AC00C30079 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		360AB8B32BC628FB00C30079 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		360AB8B52BC6294600C30079 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		36207F2B2CE1B62100A4FA32 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		362AC6F31D0522EF00B14848 /* SeeyouTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SeeyouTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		362AC6F71D0522EF00B14848 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		362EFF1F26AFEAA2008529D1 /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		364CF67D2AE2517F00E63DD4 /* libicucore.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libicucore.tbd; path = usr/lib/libicucore.tbd; sourceTree = SDKROOT; };
		364CF67F2AE2520F00E63DD4 /* libxml2.2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxml2.2.tbd; path = usr/lib/libxml2.2.tbd; sourceTree = SDKROOT; };
		364CF6802AE2524A00E63DD4 /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = usr/lib/libiconv.tbd; sourceTree = SDKROOT; };
		36580F52237D418700A8F66D /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		3667CB772DAD17B000920617 /* libswiftCoreGraphics.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libswiftCoreGraphics.tbd; path = usr/lib/swift/libswiftCoreGraphics.tbd; sourceTree = SDKROOT; };
		3670BF1C2E02ABFC00239551 /* IMYSharedExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYSharedExtension.swift; sourceTree = "<group>"; };
		3670BF1D2E02ABFC00239551 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		3670BF1E2E02ABFC00239551 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/MainInterface.storyboard; sourceTree = "<group>"; };
		3670BF202E02ABFC00239551 /* SeeyouShare.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SeeyouShare.entitlements; sourceTree = "<group>"; };
		3670BF212E02ABFC00239551 /* ShareViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShareViewController.swift; sourceTree = "<group>"; };
		3670BF272E02AC7100239551 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		3673C5212DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5222DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5232DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5242DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5252DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5262DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5272DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5282DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5292DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C52A2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C52B2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C52C2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C52D2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C52E2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C52F2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5312DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5322DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5332DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5342DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5352DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5362DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5372DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5382DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5392DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C53A2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C53B2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C53C2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C53D2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C53E2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C53F2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5412DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5422DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5432DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5442DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5452DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5462DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5472DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5482DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5492DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C54A2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C54B2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C54C2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C54D2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C54E2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C54F2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5512DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5522DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5532DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5542DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5552DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5562DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5572DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5582DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5592DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C55A2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C55B2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C55C2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C55D2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C55E2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C55F2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5612DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5622DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5632DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5642DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5652DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5662DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5672DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5682DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5692DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C56A2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C56B2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C56C2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C56D2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C56E2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C56F2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5712DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5722DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5732DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5742DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5752DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5762DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5772DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5782DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5792DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C57A2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C57B2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C57C2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C57D2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C57E2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C57F2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5812DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5822DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5832DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5842DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5852DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5862DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5872DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5882DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C5892DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C58A2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C58B2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C58C2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C58D2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C58E2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3673C58F2DBDDF2000966021 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36B2A6072387F1BC00F57601 /* BackgroundTasks.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = BackgroundTasks.framework; path = System/Library/Frameworks/BackgroundTasks.framework; sourceTree = SDKROOT; };
		36BA15D2236BFBA700923158 /* Seeyou ServiceExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Seeyou ServiceExtension.entitlements"; sourceTree = "<group>"; };
		36CBC2412C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC2422C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC2432C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC2442C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC2452C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC2462C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC2472C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC2482C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC2492C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC24A2C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC24B2C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC24C2C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC24D2C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC24E2C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36CBC24F2C23EDCB004D3030 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D177052D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D177062D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D177072D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D177082D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D177092D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D1770A2D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D1770B2D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D1770C2D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D1770D2D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D1770E2D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D1770F2D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D177102D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D177112D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D177122D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D177132D3609E40072042E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		36D384452D6D58EB0022C8C8 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		36D6FE782BC66414005C059E /* LaunchScreenNew.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LaunchScreenNew.storyboard; sourceTree = "<group>"; };
		36F224F01D05302500604259 /* SeeyouTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SeeyouTests.m; sourceTree = "<group>"; };
		36FB1FA6287539D3006BADB6 /* NSBundle+MYAPPINFO.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSBundle+MYAPPINFO.h"; sourceTree = "<group>"; };
		36FB1FA7287539D3006BADB6 /* NSBundle+MYAPPINFO.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSBundle+MYAPPINFO.m"; sourceTree = "<group>"; };
		3C1F62971DF02A3A00D082B1 /* IMYTodayWidgetNetworkHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMYTodayWidgetNetworkHelper.h; sourceTree = "<group>"; };
		3C1F62981DF02A3A00D082B1 /* IMYTodayWidgetNetworkHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMYTodayWidgetNetworkHelper.m; sourceTree = "<group>"; };
		3C2A202A1DE691BB0090DA04 /* IMYTodayWidgetHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMYTodayWidgetHelper.h; sourceTree = "<group>"; };
		3C2A202B1DE691BB0090DA04 /* IMYTodayWidgetHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMYTodayWidgetHelper.m; sourceTree = "<group>"; };
		3C2A202D1DE6920B0090DA04 /* IMYTodayArticleCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMYTodayArticleCell.h; sourceTree = "<group>"; };
		3C2A202E1DE6920B0090DA04 /* IMYTodayArticleCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMYTodayArticleCell.m; sourceTree = "<group>"; };
		3C2A20301DE692890090DA04 /* IMYTodayBottomCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMYTodayBottomCell.h; sourceTree = "<group>"; };
		3C2A20311DE692890090DA04 /* IMYTodayBottomCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMYTodayBottomCell.m; sourceTree = "<group>"; };
		3C2A20331DE698380090DA04 /* IMYTodayTopCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMYTodayTopCell.h; sourceTree = "<group>"; };
		3C2A20341DE698380090DA04 /* IMYTodayTopCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMYTodayTopCell.m; sourceTree = "<group>"; };
		3C32BABA1DE544F000C7F653 /* today-widget_advise.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "today-widget_advise.png"; sourceTree = "<group>"; };
		3C32BABB1DE544F000C7F653 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3C32BABC1DE544F000C7F653 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3C32BABD1DE544F000C7F653 /* today-widget_record.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "today-widget_record.png"; sourceTree = "<group>"; };
		3C32BABE1DE544F000C7F653 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3C32BABF1DE544F000C7F653 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3C32BAC01DE544F000C7F653 /* today-widget_search.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "today-widget_search.png"; sourceTree = "<group>"; };
		3C32BAC11DE544F000C7F653 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3C32BAC21DE544F000C7F653 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3C32BAC31DE544F000C7F653 /* today-widget_sign.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "today-widget_sign.png"; sourceTree = "<group>"; };
		3C32BAC41DE544F000C7F653 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3C32BAC51DE544F000C7F653 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		3C38DFD31DE41CA8007765FD /* Seeyou Today.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Seeyou Today.entitlements"; sourceTree = "<group>"; };
		3C6EF1041DF062BA009FF953 /* NSString+IMYTodayWidget.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+IMYTodayWidget.h"; sourceTree = "<group>"; };
		3C6EF1051DF062BA009FF953 /* NSString+IMYTodayWidget.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+IMYTodayWidget.m"; sourceTree = "<group>"; };
		3C6EF1071DF06337009FF953 /* NSObject+IMYTodayWidget.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSObject+IMYTodayWidget.h"; sourceTree = "<group>"; };
		3C6EF1081DF06337009FF953 /* NSObject+IMYTodayWidget.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSObject+IMYTodayWidget.m"; sourceTree = "<group>"; };
		3C6EF10A1DF0653A009FF953 /* NSData+IMYTodayWidget.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSData+IMYTodayWidget.h"; sourceTree = "<group>"; };
		3C6EF10B1DF0653A009FF953 /* NSData+IMYTodayWidget.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSData+IMYTodayWidget.m"; sourceTree = "<group>"; };
		3CCC6AD51DEC0A3400DFAA6E /* SYSpotlightTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SYSpotlightTest.m; sourceTree = "<group>"; };
		3CD7D61B1DE2F2E60035CECA /* Seeyou Today.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "Seeyou Today.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		3CD7D61E1DE2F2E70035CECA /* TodayViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TodayViewController.h; sourceTree = "<group>"; };
		3CD7D61F1DE2F2E70035CECA /* TodayViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TodayViewController.m; sourceTree = "<group>"; };
		3CD7D6221DE2F2E70035CECA /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/MainInterface.storyboard; sourceTree = "<group>"; };
		3CD7D6241DE2F2E70035CECA /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		3D71BF702283EF92008855AF /* HealthKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = HealthKit.framework; path = System/Library/Frameworks/HealthKit.framework; sourceTree = SDKROOT; };
		5031E9772317BF490012E4B1 /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		5031E97A2317BF640012E4B1 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		5031E97C2317BF6C0012E4B1 /* libSystem.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libSystem.tbd; path = usr/lib/libSystem.tbd; sourceTree = SDKROOT; };
		5031E97E2317BF780012E4B1 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		5031E9802317BF830012E4B1 /* CoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreServices.framework; path = System/Library/Frameworks/CoreServices.framework; sourceTree = SDKROOT; };
		5031E9822317BF9F0012E4B1 /* libobjc.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libobjc.tbd; path = usr/lib/libobjc.tbd; sourceTree = SDKROOT; };
		5031E9862317BFBD0012E4B1 /* libobjc.A.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libobjc.A.tbd; path = usr/lib/libobjc.A.tbd; sourceTree = SDKROOT; };
		5041C3E22CDD980E00505DC2 /* ExtensionDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExtensionDelegate.swift; sourceTree = "<group>"; };
		5092F8B124553A5F00249707 /* CoreSpotlight.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreSpotlight.framework; path = System/Library/Frameworks/CoreSpotlight.framework; sourceTree = SDKROOT; };
		5092F8B324553A6800249707 /* WatchConnectivity.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WatchConnectivity.framework; path = System/Library/Frameworks/WatchConnectivity.framework; sourceTree = SDKROOT; };
		532BAE8516FD801000321CFD /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = System/Library/Frameworks/ImageIO.framework; sourceTree = SDKROOT; };
		532BAE8716FD802500321CFD /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		539D0A5916FC08030092F222 /* Seeyou.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Seeyou.app; sourceTree = BUILT_PRODUCTS_DIR; };
		539D0A5C16FC08030092F222 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		539D0A5E16FC08030092F222 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		539D0A6016FC08030092F222 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		539D0B0A16FC2A120092F222 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		53F48453174C5703001EE813 /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		65AF7F4E45196A45EB0A49BE /* Pods-SeeyouWidgetExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SeeyouWidgetExtension.release.xcconfig"; path = "../Pods/Target Support Files/Pods-SeeyouWidgetExtension/Pods-SeeyouWidgetExtension.release.xcconfig"; sourceTree = "<group>"; };
		6BBEF72B7BC04E6193AB80D3 /* libPods-Seeyou.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Seeyou.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		719237411A016B70FF4EEE51 /* libPods-SeeyouIntents.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-SeeyouIntents.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		71CB06EFD380AAFACC0C7CE8 /* Pods-SeeyouIntents.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SeeyouIntents.release.xcconfig"; path = "../Pods/Target Support Files/Pods-SeeyouIntents/Pods-SeeyouIntents.release.xcconfig"; sourceTree = "<group>"; };
		7381589D2B63B1760002E1AF /* SeeyouWidgetExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SeeyouWidgetExtension.entitlements; sourceTree = "<group>"; };
		8D0651652DFAAC6800271766 /* SeeyouShare.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = SeeyouShare.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		8D0B5DB9192AF58D00BC443D /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		8D21CB392CD4A3F100069EDC /* SeeyouWatch Watch App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "SeeyouWatch Watch App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		8D240C072E49998500AA843A /* IMYContractionsWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYContractionsWidget.swift; sourceTree = "<group>"; };
		8D3361032B624D1600FA162D /* SeeyouWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = SeeyouWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		8D3361042B624D1600FA162D /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		8D3361062B624D1600FA162D /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		8D3361092B624D1700FA162D /* SeeyouWidgetBundle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeeyouWidgetBundle.swift; sourceTree = "<group>"; };
		8D33610D2B624D1700FA162D /* SeeyouWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeeyouWidget.swift; sourceTree = "<group>"; };
		8D3361102B624D1900FA162D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8D3361122B624D1900FA162D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8D3361C12B637D3100FA162D /* SmallFreeWidget01Widget.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SmallFreeWidget01Widget.swift; sourceTree = "<group>"; };
		8D49717D2CDC480800D1EF0B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		8D83EAB21AB91F1900CBE894 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		8D83EAB61AB91F6600CBE894 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		8D8517C32B999246004D7383 /* BabyPhotoVipWidget.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BabyPhotoVipWidget.swift; sourceTree = "<group>"; };
		8D8D7D9B2D559ECD00315CCF /* SeeyouIntents.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = SeeyouIntents.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		8D8D7D9C2D559ECD00315CCF /* Intents.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Intents.framework; path = System/Library/Frameworks/Intents.framework; sourceTree = SDKROOT; };
		8D8D7DA92D559ED500315CCF /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8D8D7DAA2D559ED500315CCF /* IntentHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IntentHandler.swift; sourceTree = "<group>"; };
		8D94C5742D6582AD008658C7 /* IMYTimelineProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYTimelineProvider.swift; sourceTree = "<group>"; };
		8DA56DD02D65C92700614D75 /* IMYIntentTimelineProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYIntentTimelineProvider.swift; sourceTree = "<group>"; };
		8DB3D16A2D56FFC500E71E62 /* SeeyouIntents.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SeeyouIntents.entitlements; sourceTree = "<group>"; };
		8DBD2C7F2E4C20D000F174CC /* IMYFMCountWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYFMCountWidget.swift; sourceTree = "<group>"; };
		8DBD2C842E4C2D6C00F174CC /* IMYWidgetType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IMYWidgetType.swift; sourceTree = "<group>"; };
		8DC803752D55B5F8004D205E /* Intents.intentdefinition */ = {isa = PBXFileReference; lastKnownFileType = file.intentdefinition; path = Intents.intentdefinition; sourceTree = "<group>"; };
		8DCBF8B52CD89A44008A510F /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		8DCBF8B72CD89A44008A510F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8DCBF8B82CD89A44008A510F /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		8DCBF8B92CD89A44008A510F /* SeeyouWatchApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeeyouWatchApp.swift; sourceTree = "<group>"; };
		8DD56ACE2E011C75002A71E5 /* UniformTypeIdentifiers.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UniformTypeIdentifiers.framework; path = System/Library/Frameworks/UniformTypeIdentifiers.framework; sourceTree = SDKROOT; };
		8DFFD65E2ADF6D5500FDCF9A /* SafariServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SafariServices.framework; path = System/Library/Frameworks/SafariServices.framework; sourceTree = SDKROOT; };
		990FF747E3A10795158703BF /* Pods-SeeyouWidgetExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SeeyouWidgetExtension.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-SeeyouWidgetExtension/Pods-SeeyouWidgetExtension.debug.xcconfig"; sourceTree = "<group>"; };
		99560743A9297771FE5BAC99 /* Pods-Seeyou Today.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Seeyou Today.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-Seeyou Today/Pods-Seeyou Today.debug.xcconfig"; sourceTree = "<group>"; };
		9CBAE9E5544A5BBFA3B2D532 /* Pods-SeeyouWatch Watch App.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SeeyouWatch Watch App.release.xcconfig"; path = "../Pods/Target Support Files/Pods-SeeyouWatch Watch App/Pods-SeeyouWatch Watch App.release.xcconfig"; sourceTree = "<group>"; };
		A22E297A1919E3F900867C47 /* AddressBook.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AddressBook.framework; path = System/Library/Frameworks/AddressBook.framework; sourceTree = SDKROOT; };
		A9957BD81F04D81000934FF6 /* Seeyou ServiceExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "Seeyou ServiceExtension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		A9957BDA1F04D81000934FF6 /* NotificationService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NotificationService.h; sourceTree = "<group>"; };
		A9957BDB1F04D81000934FF6 /* NotificationService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NotificationService.m; sourceTree = "<group>"; };
		A9957BDD1F04D81000934FF6 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A99640191FD66C3C0002A5E2 /* Messages.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Messages.framework; path = System/Library/Frameworks/Messages.framework; sourceTree = SDKROOT; };
		A99640401FD69DC90002A5E2 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		A99640431FD69E530002A5E2 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		ACB86C6621AD3F0500CF436B /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = System/Library/Frameworks/AVKit.framework; sourceTree = SDKROOT; };
		C5F233E71F15F7BC00817CD1 /* NSDate+IMYTodayWidget.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDate+IMYTodayWidget.h"; sourceTree = "<group>"; };
		C5F233E81F15F7BC00817CD1 /* NSDate+IMYTodayWidget.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDate+IMYTodayWidget.m"; sourceTree = "<group>"; };
		CB2247CA16FC26E600D50AC1 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		CD36F4BC08BEF504F10E2A4A /* Pods-Seeyou Today.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Seeyou Today.release.xcconfig"; path = "../Pods/Target Support Files/Pods-Seeyou Today/Pods-Seeyou Today.release.xcconfig"; sourceTree = "<group>"; };
		CD6444E2196A9F852C21005C /* Pods-SeeyouWatch Watch App.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SeeyouWatch Watch App.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-SeeyouWatch Watch App/Pods-SeeyouWatch Watch App.debug.xcconfig"; sourceTree = "<group>"; };
		D3A4B62C47A9EA009422F6B9 /* libPods-SeeyouWatch Watch App.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-SeeyouWatch Watch App.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		E33569B181AE67CC794CDA09 /* Pods-SeeyouIntents.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SeeyouIntents.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-SeeyouIntents/Pods-SeeyouIntents.debug.xcconfig"; sourceTree = "<group>"; };
		ED31CFD324FF6C44001ED2E4 /* Seeyou-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Seeyou-Bridging-Header.h"; sourceTree = "<group>"; };
		ED31CFD424FF6C44001ED2E4 /* SYTestViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SYTestViewController.swift; sourceTree = "<group>"; };
		F55C9D5C1758A789000059B9 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		F55C9D5E1758A799000059B9 /* MediaPlayer.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaPlayer.framework; path = System/Library/Frameworks/MediaPlayer.framework; sourceTree = SDKROOT; };
		F55C9D601758A7B7000059B9 /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = System/Library/Frameworks/AssetsLibrary.framework; sourceTree = SDKROOT; };
		F55C9D621758A7F3000059B9 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		F597B3AB16FF3A7200394113 /* MessageUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MessageUI.framework; path = System/Library/Frameworks/MessageUI.framework; sourceTree = SDKROOT; };
		F5A2936E1831C9E20031E803 /* CoreImage.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreImage.framework; path = System/Library/Frameworks/CoreImage.framework; sourceTree = SDKROOT; };
		F5A5FB3417276F5700018E46 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		F5C57FA517139DD700CA2312 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		F5C57FA717139DD800CA2312 /* Seeyou-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "Seeyou-Info.plist"; sourceTree = "<group>"; };
		F5C57FA817139DD800CA2312 /* Seeyou-Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "Seeyou-Prefix.pch"; sourceTree = "<group>"; };
		FEF8341F5D4676C4F76A4AF2 /* libPods-SeeyouWidgetExtension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-SeeyouWidgetExtension.a"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		362AC6F01D0522EF00B14848 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3CD7D6181DE2F2E60035CECA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				ACB86C5F21AD3EE500CF436B /* libz.tbd in Frameworks */,
				ACB86C5E21AD3EE100CF436B /* CoreTelephony.framework in Frameworks */,
				3CD7D61C1DE2F2E70035CECA /* NotificationCenter.framework in Frameworks */,
				16665D268C87ABD3FB229A98 /* libPods-Seeyou Today.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		539D0A5616FC08030092F222 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				3667CB792DAD184D00920617 /* libswiftCoreGraphics.tbd in Frameworks */,
				8DFFD6602ADF6D5C00FDCF9A /* Photos.framework in Frameworks */,
				8DFFD65F2ADF6D5500FDCF9A /* SafariServices.framework in Frameworks */,
				5031E9872317BFBD0012E4B1 /* libobjc.A.tbd in Frameworks */,
				3660D6BE2AE25AB600DB55C2 /* libxml2.2.tbd in Frameworks */,
				5031E9832317BF9F0012E4B1 /* libobjc.tbd in Frameworks */,
				5031E9812317BF830012E4B1 /* CoreServices.framework in Frameworks */,
				5031E97F2317BF780012E4B1 /* CFNetwork.framework in Frameworks */,
				3660D6C02AE25AC700DB55C2 /* libicucore.tbd in Frameworks */,
				5031E97D2317BF6C0012E4B1 /* libSystem.tbd in Frameworks */,
				5031E97B2317BF640012E4B1 /* libresolv.tbd in Frameworks */,
				5031E9792317BF500012E4B1 /* NotificationCenter.framework in Frameworks */,
				5031E9782317BF490012E4B1 /* UserNotifications.framework in Frameworks */,
				3D71BF712283EF92008855AF /* HealthKit.framework in Frameworks */,
				ACB86C6721AD3F0500CF436B /* AVKit.framework in Frameworks */,
				364CF6822AE2526600E63DD4 /* WatchConnectivity.framework in Frameworks */,
				03002D8625B03C5D00D5D2AB /* CloudKit.framework in Frameworks */,
				36039EC82151F2E4008BD5E5 /* libc++.tbd in Frameworks */,
				8D83EAB71AB91F6600CBE894 /* Security.framework in Frameworks */,
				364CF6812AE2525100E63DD4 /* libsqlite3.tbd in Frameworks */,
				F5A5FB3517276F5700018E46 /* SystemConfiguration.framework in Frameworks */,
				539D0B0B16FC2A120092F222 /* CoreTelephony.framework in Frameworks */,
				2C9B715A1A36908200DF1AE2 /* LocalAuthentication.framework in Frameworks */,
				2C1A56BB1A1AD14C005135AD /* CoreMotion.framework in Frameworks */,
				2C1E73FF19EB97CB00870CF6 /* Accelerate.framework in Frameworks */,
				539D0A6116FC08030092F222 /* CoreGraphics.framework in Frameworks */,
				A22E297B1919E3F900867C47 /* AddressBook.framework in Frameworks */,
				F5A2936F1831C9E20031E803 /* CoreImage.framework in Frameworks */,
				F55C9D631758A7F3000059B9 /* CoreMedia.framework in Frameworks */,
				F55C9D611758A7B7000059B9 /* AssetsLibrary.framework in Frameworks */,
				F55C9D5F1758A799000059B9 /* MediaPlayer.framework in Frameworks */,
				F55C9D5D1758A789000059B9 /* AVFoundation.framework in Frameworks */,
				53F48454174C5703001EE813 /* AdSupport.framework in Frameworks */,
				F597B3AC16FF3A7200394113 /* MessageUI.framework in Frameworks */,
				532BAE8816FD802500321CFD /* MobileCoreServices.framework in Frameworks */,
				5092F8B224553A5F00249707 /* CoreSpotlight.framework in Frameworks */,
				532BAE8616FD801000321CFD /* ImageIO.framework in Frameworks */,
				364CF67E2AE2518B00E63DD4 /* libz.tbd in Frameworks */,
				3660D6BF2AE25AC100DB55C2 /* libiconv.tbd in Frameworks */,
				16450DAF240CEBFA00BDA3C0 /* AuthenticationServices.framework in Frameworks */,
				CB2247CB16FC26E600D50AC1 /* QuartzCore.framework in Frameworks */,
				539D0A5D16FC08030092F222 /* UIKit.framework in Frameworks */,
				039FCF49248E658C00481DA4 /* PhotosUI.framework in Frameworks */,
				539D0A5F16FC08030092F222 /* Foundation.framework in Frameworks */,
				31821F65BB12D4FE731276A5 /* AudioToolbox.framework in Frameworks */,
				31821BB4613B845F58C509D8 /* CoreLocation.framework in Frameworks */,
				31821529EE5F54F5396488FF /* MapKit.framework in Frameworks */,
				23535E1F7602E051DD2022D4 /* libPods-Seeyou.a in Frameworks */,
				36580F53237D418800A8F66D /* StoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D0651622DFAAC6800271766 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D21CB362CD4A3F100069EDC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				9823FDBF3AD0423FEEF6F666 /* libPods-SeeyouWatch Watch App.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D3361002B624D1500FA162D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8D3361072B624D1600FA162D /* SwiftUI.framework in Frameworks */,
				8D3361052B624D1600FA162D /* WidgetKit.framework in Frameworks */,
				E9424F58177A5F60249DFFF5 /* libPods-SeeyouWidgetExtension.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D8D7D982D559ECD00315CCF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8D8D7D9D2D559ECD00315CCF /* Intents.framework in Frameworks */,
				FCBF21454AEFEFBD7247FE5A /* libPods-SeeyouIntents.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9957BD51F04D81000934FF6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0E0B9C132E49EC5A008BD3DB /* LiveActivitys */ = {
			isa = PBXGroup;
			children = (
				0E0B9C102E49EC5A008BD3DB /* BreastMilkActivityWidget.swift */,
				0E0B9C112E49EC5A008BD3DB /* FMCountingActivityWidget.swift */,
				0E0B9C122E49EC5A008BD3DB /* SleepActivityWidget.swift */,
			);
			path = LiveActivitys;
			sourceTree = "<group>";
		};
		0E47484F2E4481500092E5B5 /* Vip01 */ = {
			isa = PBXGroup;
			children = (
				0E47484E2E4481500092E5B5 /* SmallVipWidget01Widget.swift */,
			);
			path = Vip01;
			sourceTree = "<group>";
		};
		0E4748512E4481500092E5B5 /* Vip02 */ = {
			isa = PBXGroup;
			children = (
				0E4748502E4481500092E5B5 /* SmallVipWidget02Widget.swift */,
			);
			path = Vip02;
			sourceTree = "<group>";
		};
		0E4748532E4481500092E5B5 /* Vip03 */ = {
			isa = PBXGroup;
			children = (
				0E4748522E4481500092E5B5 /* MediumVipWidget03Widget.swift */,
			);
			path = Vip03;
			sourceTree = "<group>";
		};
		0E4748542E4481500092E5B5 /* PeriodInfo */ = {
			isa = PBXGroup;
			children = (
				0E47484F2E4481500092E5B5 /* Vip01 */,
				0E4748512E4481500092E5B5 /* Vip02 */,
				0E4748532E4481500092E5B5 /* Vip03 */,
			);
			path = PeriodInfo;
			sourceTree = "<group>";
		};
		0E4748592E4481590092E5B5 /* BabyFeed01 */ = {
			isa = PBXGroup;
			children = (
				0E4748582E4481590092E5B5 /* IMYBabyFeed01Widget.swift */,
			);
			path = BabyFeed01;
			sourceTree = "<group>";
		};
		0E47485C2E4481590092E5B5 /* BabyFeed02 */ = {
			isa = PBXGroup;
			children = (
				0E47485A2E4481590092E5B5 /* IMYBabyFeed02Widget.swift */,
				0E47485B2E4481590092E5B5 /* IMYBaByFeed02WidgetAppItents.swift */,
			);
			path = BabyFeed02;
			sourceTree = "<group>";
		};
		0E47485E2E4481590092E5B5 /* BabyFeed03 */ = {
			isa = PBXGroup;
			children = (
				0E47485D2E4481590092E5B5 /* IMYBabyFeed03Widget.swift */,
			);
			path = BabyFeed03;
			sourceTree = "<group>";
		};
		0E47485F2E4481590092E5B5 /* BabyFeed */ = {
			isa = PBXGroup;
			children = (
				0E4748592E4481590092E5B5 /* BabyFeed01 */,
				0E47485C2E4481590092E5B5 /* BabyFeed02 */,
				0E47485E2E4481590092E5B5 /* BabyFeed03 */,
			);
			path = BabyFeed;
			sourceTree = "<group>";
		};
		362AC6F41D0522EF00B14848 /* SeeyouTests */ = {
			isa = PBXGroup;
			children = (
				3CCC6AD41DEC09EB00DFAA6E /* Spotlight */,
				36F224F01D05302500604259 /* SeeyouTests.m */,
				362AC6F71D0522EF00B14848 /* Info.plist */,
			);
			path = SeeyouTests;
			sourceTree = "<group>";
		};
		3670BF222E02ABFC00239551 /* SeeyouShare */ = {
			isa = PBXGroup;
			children = (
				3670BF272E02AC7100239551 /* PrivacyInfo.xcprivacy */,
				3670BF1C2E02ABFC00239551 /* IMYSharedExtension.swift */,
				3670BF1D2E02ABFC00239551 /* Info.plist */,
				3670BF1F2E02ABFC00239551 /* MainInterface.storyboard */,
				3670BF202E02ABFC00239551 /* SeeyouShare.entitlements */,
				3670BF212E02ABFC00239551 /* ShareViewController.swift */,
			);
			path = SeeyouShare;
			sourceTree = "<group>";
		};
		3673C5302DBDDF2000966021 /* AppIcon-VIP-3C */ = {
			isa = PBXGroup;
			children = (
				3673C5212DBDDF2000966021 /* <EMAIL> */,
				3673C5222DBDDF2000966021 /* <EMAIL> */,
				3673C5232DBDDF2000966021 /* <EMAIL> */,
				3673C5242DBDDF2000966021 /* <EMAIL> */,
				3673C5252DBDDF2000966021 /* <EMAIL> */,
				3673C5262DBDDF2000966021 /* <EMAIL> */,
				3673C5272DBDDF2000966021 /* <EMAIL> */,
				3673C5282DBDDF2000966021 /* <EMAIL> */,
				3673C5292DBDDF2000966021 /* <EMAIL> */,
				3673C52A2DBDDF2000966021 /* <EMAIL> */,
				3673C52B2DBDDF2000966021 /* <EMAIL> */,
				3673C52C2DBDDF2000966021 /* <EMAIL> */,
				3673C52D2DBDDF2000966021 /* <EMAIL> */,
				3673C52E2DBDDF2000966021 /* <EMAIL> */,
				3673C52F2DBDDF2000966021 /* <EMAIL> */,
			);
			path = "AppIcon-VIP-3C";
			sourceTree = "<group>";
		};
		3673C5402DBDDF2000966021 /* AppIcon-VIP-4C */ = {
			isa = PBXGroup;
			children = (
				3673C5312DBDDF2000966021 /* <EMAIL> */,
				3673C5322DBDDF2000966021 /* <EMAIL> */,
				3673C5332DBDDF2000966021 /* <EMAIL> */,
				3673C5342DBDDF2000966021 /* <EMAIL> */,
				3673C5352DBDDF2000966021 /* <EMAIL> */,
				3673C5362DBDDF2000966021 /* <EMAIL> */,
				3673C5372DBDDF2000966021 /* <EMAIL> */,
				3673C5382DBDDF2000966021 /* <EMAIL> */,
				3673C5392DBDDF2000966021 /* <EMAIL> */,
				3673C53A2DBDDF2000966021 /* <EMAIL> */,
				3673C53B2DBDDF2000966021 /* <EMAIL> */,
				3673C53C2DBDDF2000966021 /* <EMAIL> */,
				3673C53D2DBDDF2000966021 /* <EMAIL> */,
				3673C53E2DBDDF2000966021 /* <EMAIL> */,
				3673C53F2DBDDF2000966021 /* <EMAIL> */,
			);
			path = "AppIcon-VIP-4C";
			sourceTree = "<group>";
		};
		3673C5502DBDDF2000966021 /* AppIcon-VIP-5C */ = {
			isa = PBXGroup;
			children = (
				3673C5412DBDDF2000966021 /* <EMAIL> */,
				3673C5422DBDDF2000966021 /* <EMAIL> */,
				3673C5432DBDDF2000966021 /* <EMAIL> */,
				3673C5442DBDDF2000966021 /* <EMAIL> */,
				3673C5452DBDDF2000966021 /* <EMAIL> */,
				3673C5462DBDDF2000966021 /* <EMAIL> */,
				3673C5472DBDDF2000966021 /* <EMAIL> */,
				3673C5482DBDDF2000966021 /* <EMAIL> */,
				3673C5492DBDDF2000966021 /* <EMAIL> */,
				3673C54A2DBDDF2000966021 /* <EMAIL> */,
				3673C54B2DBDDF2000966021 /* <EMAIL> */,
				3673C54C2DBDDF2000966021 /* <EMAIL> */,
				3673C54D2DBDDF2000966021 /* <EMAIL> */,
				3673C54E2DBDDF2000966021 /* <EMAIL> */,
				3673C54F2DBDDF2000966021 /* <EMAIL> */,
			);
			path = "AppIcon-VIP-5C";
			sourceTree = "<group>";
		};
		3673C5602DBDDF2000966021 /* AppIcon-VIP-6C */ = {
			isa = PBXGroup;
			children = (
				3673C5512DBDDF2000966021 /* <EMAIL> */,
				3673C5522DBDDF2000966021 /* <EMAIL> */,
				3673C5532DBDDF2000966021 /* <EMAIL> */,
				3673C5542DBDDF2000966021 /* <EMAIL> */,
				3673C5552DBDDF2000966021 /* <EMAIL> */,
				3673C5562DBDDF2000966021 /* <EMAIL> */,
				3673C5572DBDDF2000966021 /* <EMAIL> */,
				3673C5582DBDDF2000966021 /* <EMAIL> */,
				3673C5592DBDDF2000966021 /* <EMAIL> */,
				3673C55A2DBDDF2000966021 /* <EMAIL> */,
				3673C55B2DBDDF2000966021 /* <EMAIL> */,
				3673C55C2DBDDF2000966021 /* <EMAIL> */,
				3673C55D2DBDDF2000966021 /* <EMAIL> */,
				3673C55E2DBDDF2000966021 /* <EMAIL> */,
				3673C55F2DBDDF2000966021 /* <EMAIL> */,
			);
			path = "AppIcon-VIP-6C";
			sourceTree = "<group>";
		};
		3673C5702DBDDF2000966021 /* AppIcon-VIP-7C */ = {
			isa = PBXGroup;
			children = (
				3673C5612DBDDF2000966021 /* <EMAIL> */,
				3673C5622DBDDF2000966021 /* <EMAIL> */,
				3673C5632DBDDF2000966021 /* <EMAIL> */,
				3673C5642DBDDF2000966021 /* <EMAIL> */,
				3673C5652DBDDF2000966021 /* <EMAIL> */,
				3673C5662DBDDF2000966021 /* <EMAIL> */,
				3673C5672DBDDF2000966021 /* <EMAIL> */,
				3673C5682DBDDF2000966021 /* <EMAIL> */,
				3673C5692DBDDF2000966021 /* <EMAIL> */,
				3673C56A2DBDDF2000966021 /* <EMAIL> */,
				3673C56B2DBDDF2000966021 /* <EMAIL> */,
				3673C56C2DBDDF2000966021 /* <EMAIL> */,
				3673C56D2DBDDF2000966021 /* <EMAIL> */,
				3673C56E2DBDDF2000966021 /* <EMAIL> */,
				3673C56F2DBDDF2000966021 /* <EMAIL> */,
			);
			path = "AppIcon-VIP-7C";
			sourceTree = "<group>";
		};
		3673C5802DBDDF2000966021 /* AppIcon-VIP-8C */ = {
			isa = PBXGroup;
			children = (
				3673C5712DBDDF2000966021 /* <EMAIL> */,
				3673C5722DBDDF2000966021 /* <EMAIL> */,
				3673C5732DBDDF2000966021 /* <EMAIL> */,
				3673C5742DBDDF2000966021 /* <EMAIL> */,
				3673C5752DBDDF2000966021 /* <EMAIL> */,
				3673C5762DBDDF2000966021 /* <EMAIL> */,
				3673C5772DBDDF2000966021 /* <EMAIL> */,
				3673C5782DBDDF2000966021 /* <EMAIL> */,
				3673C5792DBDDF2000966021 /* <EMAIL> */,
				3673C57A2DBDDF2000966021 /* <EMAIL> */,
				3673C57B2DBDDF2000966021 /* <EMAIL> */,
				3673C57C2DBDDF2000966021 /* <EMAIL> */,
				3673C57D2DBDDF2000966021 /* <EMAIL> */,
				3673C57E2DBDDF2000966021 /* <EMAIL> */,
				3673C57F2DBDDF2000966021 /* <EMAIL> */,
			);
			path = "AppIcon-VIP-8C";
			sourceTree = "<group>";
		};
		3673C5902DBDDF2000966021 /* AppIcon-VIP-9C */ = {
			isa = PBXGroup;
			children = (
				3673C5812DBDDF2000966021 /* <EMAIL> */,
				3673C5822DBDDF2000966021 /* <EMAIL> */,
				3673C5832DBDDF2000966021 /* <EMAIL> */,
				3673C5842DBDDF2000966021 /* <EMAIL> */,
				3673C5852DBDDF2000966021 /* <EMAIL> */,
				3673C5862DBDDF2000966021 /* <EMAIL> */,
				3673C5872DBDDF2000966021 /* <EMAIL> */,
				3673C5882DBDDF2000966021 /* <EMAIL> */,
				3673C5892DBDDF2000966021 /* <EMAIL> */,
				3673C58A2DBDDF2000966021 /* <EMAIL> */,
				3673C58B2DBDDF2000966021 /* <EMAIL> */,
				3673C58C2DBDDF2000966021 /* <EMAIL> */,
				3673C58D2DBDDF2000966021 /* <EMAIL> */,
				3673C58E2DBDDF2000966021 /* <EMAIL> */,
				3673C58F2DBDDF2000966021 /* <EMAIL> */,
			);
			path = "AppIcon-VIP-9C";
			sourceTree = "<group>";
		};
		36B613D9282D0CB400632AA1 /* Resources */ = {
			isa = PBXGroup;
			children = (
				36CDA2CE2C23D0E9008A3B13 /* AppIcon-QinYou */,
				36D177142D3609E40072042E /* AppIcon-VIP */,
				3673C5302DBDDF2000966021 /* AppIcon-VIP-3C */,
				3673C5402DBDDF2000966021 /* AppIcon-VIP-4C */,
				3673C5502DBDDF2000966021 /* AppIcon-VIP-5C */,
				3673C5602DBDDF2000966021 /* AppIcon-VIP-6C */,
				3673C5702DBDDF2000966021 /* AppIcon-VIP-7C */,
				3673C5802DBDDF2000966021 /* AppIcon-VIP-8C */,
				3673C5902DBDDF2000966021 /* AppIcon-VIP-9C */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		36CDA2CE2C23D0E9008A3B13 /* AppIcon-QinYou */ = {
			isa = PBXGroup;
			children = (
				36CBC24A2C23EDCB004D3030 /* <EMAIL> */,
				36CBC2422C23EDCB004D3030 /* <EMAIL> */,
				36CBC2432C23EDCB004D3030 /* <EMAIL> */,
				36CBC24B2C23EDCB004D3030 /* <EMAIL> */,
				36CBC24E2C23EDCB004D3030 /* <EMAIL> */,
				36CBC2412C23EDCB004D3030 /* <EMAIL> */,
				36CBC2452C23EDCB004D3030 /* <EMAIL> */,
				36CBC24D2C23EDCB004D3030 /* <EMAIL> */,
				36CBC2462C23EDCB004D3030 /* <EMAIL> */,
				36CBC2442C23EDCB004D3030 /* <EMAIL> */,
				36CBC2492C23EDCB004D3030 /* <EMAIL> */,
				36CBC24F2C23EDCB004D3030 /* <EMAIL> */,
				36CBC24C2C23EDCB004D3030 /* <EMAIL> */,
				36CBC2472C23EDCB004D3030 /* <EMAIL> */,
				36CBC2482C23EDCB004D3030 /* <EMAIL> */,
			);
			path = "AppIcon-QinYou";
			sourceTree = "<group>";
		};
		36D177142D3609E40072042E /* AppIcon-VIP */ = {
			isa = PBXGroup;
			children = (
				36D177052D3609E40072042E /* <EMAIL> */,
				36D177062D3609E40072042E /* <EMAIL> */,
				36D177072D3609E40072042E /* <EMAIL> */,
				36D177082D3609E40072042E /* <EMAIL> */,
				36D177092D3609E40072042E /* <EMAIL> */,
				36D1770A2D3609E40072042E /* <EMAIL> */,
				36D1770B2D3609E40072042E /* <EMAIL> */,
				36D1770C2D3609E40072042E /* <EMAIL> */,
				36D1770D2D3609E40072042E /* <EMAIL> */,
				36D1770E2D3609E40072042E /* <EMAIL> */,
				36D1770F2D3609E40072042E /* <EMAIL> */,
				36D177102D3609E40072042E /* <EMAIL> */,
				36D177112D3609E40072042E /* <EMAIL> */,
				36D177122D3609E40072042E /* <EMAIL> */,
				36D177132D3609E40072042E /* <EMAIL> */,
			);
			path = "AppIcon-VIP";
			sourceTree = "<group>";
		};
		36FB1FA5287539B3006BADB6 /* Classes */ = {
			isa = PBXGroup;
			children = (
				36FB1FA6287539D3006BADB6 /* NSBundle+MYAPPINFO.h */,
				36FB1FA7287539D3006BADB6 /* NSBundle+MYAPPINFO.m */,
			);
			path = Classes;
			sourceTree = "<group>";
		};
		38268F81C5B2679AC1EBF0EF /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		3C32BAB91DE544D800C7F653 /* Resource */ = {
			isa = PBXGroup;
			children = (
				3C32BABA1DE544F000C7F653 /* today-widget_advise.png */,
				3C32BABB1DE544F000C7F653 /* <EMAIL> */,
				3C32BABC1DE544F000C7F653 /* <EMAIL> */,
				3C32BABD1DE544F000C7F653 /* today-widget_record.png */,
				3C32BABE1DE544F000C7F653 /* <EMAIL> */,
				3C32BABF1DE544F000C7F653 /* <EMAIL> */,
				3C32BAC01DE544F000C7F653 /* today-widget_search.png */,
				3C32BAC11DE544F000C7F653 /* <EMAIL> */,
				3C32BAC21DE544F000C7F653 /* <EMAIL> */,
				3C32BAC31DE544F000C7F653 /* today-widget_sign.png */,
				3C32BAC41DE544F000C7F653 /* <EMAIL> */,
				3C32BAC51DE544F000C7F653 /* <EMAIL> */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		3CCC6AD41DEC09EB00DFAA6E /* Spotlight */ = {
			isa = PBXGroup;
			children = (
				3CCC6AD51DEC0A3400DFAA6E /* SYSpotlightTest.m */,
			);
			path = Spotlight;
			sourceTree = "<group>";
		};
		3CD7D61D1DE2F2E70035CECA /* Seeyou Today */ = {
			isa = PBXGroup;
			children = (
				3C32BAB91DE544D800C7F653 /* Resource */,
				3C38DFD31DE41CA8007765FD /* Seeyou Today.entitlements */,
				3CD7D61E1DE2F2E70035CECA /* TodayViewController.h */,
				3CD7D61F1DE2F2E70035CECA /* TodayViewController.m */,
				3CD7D6211DE2F2E70035CECA /* MainInterface.storyboard */,
				3CD7D6241DE2F2E70035CECA /* Info.plist */,
				3C2A202A1DE691BB0090DA04 /* IMYTodayWidgetHelper.h */,
				3C2A202B1DE691BB0090DA04 /* IMYTodayWidgetHelper.m */,
				3C1F62971DF02A3A00D082B1 /* IMYTodayWidgetNetworkHelper.h */,
				3C1F62981DF02A3A00D082B1 /* IMYTodayWidgetNetworkHelper.m */,
				3C2A20331DE698380090DA04 /* IMYTodayTopCell.h */,
				3C2A20341DE698380090DA04 /* IMYTodayTopCell.m */,
				3C2A202D1DE6920B0090DA04 /* IMYTodayArticleCell.h */,
				3C2A202E1DE6920B0090DA04 /* IMYTodayArticleCell.m */,
				3C2A20301DE692890090DA04 /* IMYTodayBottomCell.h */,
				3C2A20311DE692890090DA04 /* IMYTodayBottomCell.m */,
				3C6EF1041DF062BA009FF953 /* NSString+IMYTodayWidget.h */,
				3C6EF1051DF062BA009FF953 /* NSString+IMYTodayWidget.m */,
				3C6EF1071DF06337009FF953 /* NSObject+IMYTodayWidget.h */,
				3C6EF1081DF06337009FF953 /* NSObject+IMYTodayWidget.m */,
				3C6EF10A1DF0653A009FF953 /* NSData+IMYTodayWidget.h */,
				3C6EF10B1DF0653A009FF953 /* NSData+IMYTodayWidget.m */,
				C5F233E71F15F7BC00817CD1 /* NSDate+IMYTodayWidget.h */,
				C5F233E81F15F7BC00817CD1 /* NSDate+IMYTodayWidget.m */,
				360AB8B32BC628FB00C30079 /* PrivacyInfo.xcprivacy */,
			);
			path = "Seeyou Today";
			sourceTree = "<group>";
		};
		539D0A5016FC08030092F222 = {
			isa = PBXGroup;
			children = (
				F5C57EBA17139DD700CA2312 /* Seeyou */,
				362AC6F41D0522EF00B14848 /* SeeyouTests */,
				3CD7D61D1DE2F2E70035CECA /* Seeyou Today */,
				A9957BD91F04D81000934FF6 /* Seeyou ServiceExtension */,
				8D3361082B624D1700FA162D /* SeeyouWidget */,
				8DCBF8BA2CD89A44008A510F /* SeeyouWatch Watch App */,
				8D8D7DAB2D559ED500315CCF /* SeeyouIntents */,
				3670BF222E02ABFC00239551 /* SeeyouShare */,
				539D0A5B16FC08030092F222 /* Frameworks */,
				539D0A5A16FC08030092F222 /* Products */,
				717B25CA2973E6A4C7636D9A /* Pods */,
			);
			sourceTree = "<group>";
		};
		539D0A5A16FC08030092F222 /* Products */ = {
			isa = PBXGroup;
			children = (
				539D0A5916FC08030092F222 /* Seeyou.app */,
				362AC6F31D0522EF00B14848 /* SeeyouTests.xctest */,
				3CD7D61B1DE2F2E60035CECA /* Seeyou Today.appex */,
				A9957BD81F04D81000934FF6 /* Seeyou ServiceExtension.appex */,
				8D3361032B624D1600FA162D /* SeeyouWidgetExtension.appex */,
				8D21CB392CD4A3F100069EDC /* SeeyouWatch Watch App.app */,
				8D8D7D9B2D559ECD00315CCF /* SeeyouIntents.appex */,
				8D0651652DFAAC6800271766 /* SeeyouShare.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		539D0A5B16FC08030092F222 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				3667CB772DAD17B000920617 /* libswiftCoreGraphics.tbd */,
				364CF6802AE2524A00E63DD4 /* libiconv.tbd */,
				364CF67F2AE2520F00E63DD4 /* libxml2.2.tbd */,
				364CF67D2AE2517F00E63DD4 /* libicucore.tbd */,
				5031E9862317BFBD0012E4B1 /* libobjc.A.tbd */,
				5031E9822317BF9F0012E4B1 /* libobjc.tbd */,
				36039EC72151F2E3008BD5E5 /* libc++.tbd */,
				A99640431FD69E530002A5E2 /* libz.tbd */,
				A99640401FD69DC90002A5E2 /* libsqlite3.tbd */,
				5031E97C2317BF6C0012E4B1 /* libSystem.tbd */,
				5031E97A2317BF640012E4B1 /* libresolv.tbd */,
				8DFFD65E2ADF6D5500FDCF9A /* SafariServices.framework */,
				362EFF1F26AFEAA2008529D1 /* JavaScriptCore.framework */,
				03002D8525B03C5D00D5D2AB /* CloudKit.framework */,
				039FCF48248E658C00481DA4 /* PhotosUI.framework */,
				5092F8B324553A6800249707 /* WatchConnectivity.framework */,
				5092F8B124553A5F00249707 /* CoreSpotlight.framework */,
				16450DAE240CEBFA00BDA3C0 /* AuthenticationServices.framework */,
				36B2A6072387F1BC00F57601 /* BackgroundTasks.framework */,
				36580F52237D418700A8F66D /* StoreKit.framework */,
				5031E9802317BF830012E4B1 /* CoreServices.framework */,
				5031E97E2317BF780012E4B1 /* CFNetwork.framework */,
				5031E9772317BF490012E4B1 /* UserNotifications.framework */,
				3D71BF702283EF92008855AF /* HealthKit.framework */,
				ACB86C6621AD3F0500CF436B /* AVKit.framework */,
				8D83EAB61AB91F6600CBE894 /* Security.framework */,
				8D83EAB21AB91F1900CBE894 /* OpenGLES.framework */,
				2C9B71591A36908200DF1AE2 /* LocalAuthentication.framework */,
				2C1A56BC1A1AF504005135AD /* Photos.framework */,
				2C1E73FE19EB97CB00870CF6 /* Accelerate.framework */,
				8D0B5DB9192AF58D00BC443D /* CoreMotion.framework */,
				A22E297A1919E3F900867C47 /* AddressBook.framework */,
				F5A2936E1831C9E20031E803 /* CoreImage.framework */,
				F55C9D621758A7F3000059B9 /* CoreMedia.framework */,
				F55C9D601758A7B7000059B9 /* AssetsLibrary.framework */,
				F55C9D5E1758A799000059B9 /* MediaPlayer.framework */,
				F55C9D5C1758A789000059B9 /* AVFoundation.framework */,
				F5A5FB3417276F5700018E46 /* SystemConfiguration.framework */,
				F597B3AB16FF3A7200394113 /* MessageUI.framework */,
				532BAE8716FD802500321CFD /* MobileCoreServices.framework */,
				532BAE8516FD801000321CFD /* ImageIO.framework */,
				539D0B0A16FC2A120092F222 /* CoreTelephony.framework */,
				CB2247CA16FC26E600D50AC1 /* QuartzCore.framework */,
				539D0A5C16FC08030092F222 /* UIKit.framework */,
				539D0A5E16FC08030092F222 /* Foundation.framework */,
				539D0A6016FC08030092F222 /* CoreGraphics.framework */,
				6BBEF72B7BC04E6193AB80D3 /* libPods-Seeyou.a */,
				53F48453174C5703001EE813 /* AdSupport.framework */,
				318216A4B9460EFC9E11FB51 /* AudioToolbox.framework */,
				31821C4046377D022A838225 /* CoreLocation.framework */,
				318219E03343363FD8C93B0F /* MapKit.framework */,
				2C5C9C9519F6448B004C2C4A /* NotificationCenter.framework */,
				1466F596ECC91FDDD943FB88 /* libPods-Seeyou Today.a */,
				A99640191FD66C3C0002A5E2 /* Messages.framework */,
				8D3361042B624D1600FA162D /* WidgetKit.framework */,
				8D3361062B624D1600FA162D /* SwiftUI.framework */,
				FEF8341F5D4676C4F76A4AF2 /* libPods-SeeyouWidgetExtension.a */,
				D3A4B62C47A9EA009422F6B9 /* libPods-SeeyouWatch Watch App.a */,
				8D8D7D9C2D559ECD00315CCF /* Intents.framework */,
				719237411A016B70FF4EEE51 /* libPods-SeeyouIntents.a */,
				8DD56ACE2E011C75002A71E5 /* UniformTypeIdentifiers.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		717B25CA2973E6A4C7636D9A /* Pods */ = {
			isa = PBXGroup;
			children = (
				1DC825B878CA0EF815FE7A92 /* Pods-Seeyou.debug.xcconfig */,
				25389EDDBC1F5D4523833381 /* Pods-Seeyou.release.xcconfig */,
				38268F81C5B2679AC1EBF0EF /* Products */,
				99560743A9297771FE5BAC99 /* Pods-Seeyou Today.debug.xcconfig */,
				CD36F4BC08BEF504F10E2A4A /* Pods-Seeyou Today.release.xcconfig */,
				990FF747E3A10795158703BF /* Pods-SeeyouWidgetExtension.debug.xcconfig */,
				65AF7F4E45196A45EB0A49BE /* Pods-SeeyouWidgetExtension.release.xcconfig */,
				CD6444E2196A9F852C21005C /* Pods-SeeyouWatch Watch App.debug.xcconfig */,
				9CBAE9E5544A5BBFA3B2D532 /* Pods-SeeyouWatch Watch App.release.xcconfig */,
				E33569B181AE67CC794CDA09 /* Pods-SeeyouIntents.debug.xcconfig */,
				71CB06EFD380AAFACC0C7CE8 /* Pods-SeeyouIntents.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		8D240C062E49992A00AA843A /* Pregnant */ = {
			isa = PBXGroup;
			children = (
				8D240C072E49998500AA843A /* IMYContractionsWidget.swift */,
				8DBD2C7F2E4C20D000F174CC /* IMYFMCountWidget.swift */,
			);
			path = Pregnant;
			sourceTree = "<group>";
		};
		8D3361082B624D1700FA162D /* SeeyouWidget */ = {
			isa = PBXGroup;
			children = (
				0E0B9C132E49EC5A008BD3DB /* LiveActivitys */,
				8D3361BF2B637D1F00FA162D /* Widgets */,
				8D3361092B624D1700FA162D /* SeeyouWidgetBundle.swift */,
				8D33610D2B624D1700FA162D /* SeeyouWidget.swift */,
				8D3361102B624D1900FA162D /* Assets.xcassets */,
				8D3361122B624D1900FA162D /* Info.plist */,
				8DC803752D55B5F8004D205E /* Intents.intentdefinition */,
				7381589D2B63B1760002E1AF /* SeeyouWidgetExtension.entitlements */,
				360AB8B52BC6294600C30079 /* PrivacyInfo.xcprivacy */,
			);
			path = SeeyouWidget;
			sourceTree = "<group>";
		};
		8D3361BF2B637D1F00FA162D /* Widgets */ = {
			isa = PBXGroup;
			children = (
				8D240C062E49992A00AA843A /* Pregnant */,
				8D3361C02B637D3100FA162D /* Free01 */,
				0E4748542E4481500092E5B5 /* PeriodInfo */,
				0E47485F2E4481590092E5B5 /* BabyFeed */,
				8D8517C22B9991A2004D7383 /* BabyPhoto */,
				8D94C5722D65828F008658C7 /* Base */,
				8DBD2C842E4C2D6C00F174CC /* IMYWidgetType.swift */,
			);
			path = Widgets;
			sourceTree = "<group>";
		};
		8D3361C02B637D3100FA162D /* Free01 */ = {
			isa = PBXGroup;
			children = (
				8D3361C12B637D3100FA162D /* SmallFreeWidget01Widget.swift */,
			);
			path = Free01;
			sourceTree = "<group>";
		};
		8D8517C22B9991A2004D7383 /* BabyPhoto */ = {
			isa = PBXGroup;
			children = (
				8D8517C32B999246004D7383 /* BabyPhotoVipWidget.swift */,
			);
			path = BabyPhoto;
			sourceTree = "<group>";
		};
		8D8D7DAB2D559ED500315CCF /* SeeyouIntents */ = {
			isa = PBXGroup;
			children = (
				36D384452D6D58EB0022C8C8 /* PrivacyInfo.xcprivacy */,
				8DB3D16A2D56FFC500E71E62 /* SeeyouIntents.entitlements */,
				8D8D7DA92D559ED500315CCF /* Info.plist */,
				8D8D7DAA2D559ED500315CCF /* IntentHandler.swift */,
			);
			path = SeeyouIntents;
			sourceTree = "<group>";
		};
		8D94C5722D65828F008658C7 /* Base */ = {
			isa = PBXGroup;
			children = (
				8D94C5732D6582A4008658C7 /* IMYTimelineProvider */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		8D94C5732D6582A4008658C7 /* IMYTimelineProvider */ = {
			isa = PBXGroup;
			children = (
				8D94C5742D6582AD008658C7 /* IMYTimelineProvider.swift */,
				8DA56DD02D65C92700614D75 /* IMYIntentTimelineProvider.swift */,
			);
			path = IMYTimelineProvider;
			sourceTree = "<group>";
		};
		8DCBF8B62CD89A44008A510F /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				8DCBF8B52CD89A44008A510F /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		8DCBF8BA2CD89A44008A510F /* SeeyouWatch Watch App */ = {
			isa = PBXGroup;
			children = (
				3600E8F92CE1F27900CD2D1E /* SeeyouWatchApp.entitlements */,
				36207F2B2CE1B62100A4FA32 /* Info.plist */,
				8D49717D2CDC480800D1EF0B /* PrivacyInfo.xcprivacy */,
				8DCBF8B62CD89A44008A510F /* Preview Content */,
				8DCBF8B72CD89A44008A510F /* Assets.xcassets */,
				5041C3E22CDD980E00505DC2 /* ExtensionDelegate.swift */,
				8DCBF8B82CD89A44008A510F /* ContentView.swift */,
				8DCBF8B92CD89A44008A510F /* SeeyouWatchApp.swift */,
			);
			path = "SeeyouWatch Watch App";
			sourceTree = "<group>";
		};
		A9957BD91F04D81000934FF6 /* Seeyou ServiceExtension */ = {
			isa = PBXGroup;
			children = (
				36BA15D2236BFBA700923158 /* Seeyou ServiceExtension.entitlements */,
				A9957BDA1F04D81000934FF6 /* NotificationService.h */,
				A9957BDB1F04D81000934FF6 /* NotificationService.m */,
				A9957BDD1F04D81000934FF6 /* Info.plist */,
				360AB8B12BC628AC00C30079 /* PrivacyInfo.xcprivacy */,
			);
			path = "Seeyou ServiceExtension";
			sourceTree = "<group>";
		};
		CBE4304817156108003CFCA6 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				F5C57FA517139DD700CA2312 /* main.m */,
				F5C57FA717139DD800CA2312 /* Seeyou-Info.plist */,
				F5C57FA817139DD800CA2312 /* Seeyou-Prefix.pch */,
				360AB8AF2BC624A100C30079 /* PrivacyInfo.xcprivacy */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		F5C57EBA17139DD700CA2312 /* Seeyou */ = {
			isa = PBXGroup;
			children = (
				36FB1FA5287539B3006BADB6 /* Classes */,
				36B613D9282D0CB400632AA1 /* Resources */,
				36D6FE782BC66414005C059E /* LaunchScreenNew.storyboard */,
				2CA58E5919EE20DE00B369D5 /* Images.xcassets */,
				2C4B5A331A1052DA00D3A13E /* Seeyou.entitlements */,
				CBE4304817156108003CFCA6 /* Supporting Files */,
				ED31CFD424FF6C44001ED2E4 /* SYTestViewController.swift */,
				ED31CFD324FF6C44001ED2E4 /* Seeyou-Bridging-Header.h */,
			);
			path = Seeyou;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		362AC6F21D0522EF00B14848 /* SeeyouTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 362AC6FA1D0522EF00B14848 /* Build configuration list for PBXNativeTarget "SeeyouTests" */;
			buildPhases = (
				362AC6EF1D0522EF00B14848 /* Sources */,
				362AC6F01D0522EF00B14848 /* Frameworks */,
				362AC6F11D0522EF00B14848 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				362AC6F91D0522EF00B14848 /* PBXTargetDependency */,
			);
			name = SeeyouTests;
			productName = SeeyouTests;
			productReference = 362AC6F31D0522EF00B14848 /* SeeyouTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		3CD7D61A1DE2F2E60035CECA /* Seeyou Today */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3CD7D62A1DE2F2E70035CECA /* Build configuration list for PBXNativeTarget "Seeyou Today" */;
			buildPhases = (
				1CB62C40BE782E8493B2A20D /* [CP] Check Pods Manifest.lock */,
				3CD7D6171DE2F2E60035CECA /* Sources */,
				3CD7D6181DE2F2E60035CECA /* Frameworks */,
				3CD7D6191DE2F2E60035CECA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Seeyou Today";
			productName = "Seeyou Today";
			productReference = 3CD7D61B1DE2F2E60035CECA /* Seeyou Today.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		539D0A5816FC08030092F222 /* Seeyou */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 539D0A7C16FC08030092F222 /* Build configuration list for PBXNativeTarget "Seeyou" */;
			buildPhases = (
				14EB7BB19D56C3C71B0F6AA3 /* [CP] Check Pods Manifest.lock */,
				539D0A5516FC08030092F222 /* Sources */,
				539D0A5616FC08030092F222 /* Frameworks */,
				539D0A5716FC08030092F222 /* Resources */,
				B92FCA5407D9C30F72DB3B4D /* [CP] Copy Pods Resources */,
				A12C40331B7F42A90023323F /* Embed App Extensions */,
				36EFABD01CD3B4C800859777 /* Check Deduplication Resources  */,
				50BAB38F257DFB5900FFDA5A /* Copy Root Resources */,
				380AADA38609879FE2136C51 /* [CP] Embed Pods Frameworks */,
				03FEE06C1DC8B795008FF235 /* Embed Watch Content */,
				5053496927B3A5D2001003D5 /* Make Cache Assets */,
			);
			buildRules = (
			);
			dependencies = (
				3CD7D6261DE2F2E70035CECA /* PBXTargetDependency */,
				A9957BDF1F04D81000934FF6 /* PBXTargetDependency */,
				8D3361162B624D1900FA162D /* PBXTargetDependency */,
				8D21CB452CD4A3F300069EDC /* PBXTargetDependency */,
				8D8D7DA32D559ECD00315CCF /* PBXTargetDependency */,
				8D06516E2DFAAC6900271766 /* PBXTargetDependency */,
			);
			name = Seeyou;
			productName = Seeyou;
			productReference = 539D0A5916FC08030092F222 /* Seeyou.app */;
			productType = "com.apple.product-type.application";
		};
		8D0651642DFAAC6800271766 /* SeeyouShare */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8D0651712DFAAC6900271766 /* Build configuration list for PBXNativeTarget "SeeyouShare" */;
			buildPhases = (
				8D0651612DFAAC6800271766 /* Sources */,
				8D0651622DFAAC6800271766 /* Frameworks */,
				8D0651632DFAAC6800271766 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SeeyouShare;
			productName = SeeyouShare;
			productReference = 8D0651652DFAAC6800271766 /* SeeyouShare.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		8D21CB382CD4A3F100069EDC /* SeeyouWatch Watch App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8D21CB472CD4A3F300069EDC /* Build configuration list for PBXNativeTarget "SeeyouWatch Watch App" */;
			buildPhases = (
				1CDCD15FBC54B6AE70C44B22 /* [CP] Check Pods Manifest.lock */,
				8D21CB352CD4A3F100069EDC /* Sources */,
				8D21CB362CD4A3F100069EDC /* Frameworks */,
				8D21CB372CD4A3F100069EDC /* Resources */,
				AD117322B4D452DB4D2C1CB3 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SeeyouWatch Watch App";
			productName = "SeeyouWatch Watch App";
			productReference = 8D21CB392CD4A3F100069EDC /* SeeyouWatch Watch App.app */;
			productType = "com.apple.product-type.application";
		};
		8D3361022B624D1500FA162D /* SeeyouWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8D3361182B624D1E00FA162D /* Build configuration list for PBXNativeTarget "SeeyouWidgetExtension" */;
			buildPhases = (
				715E433F2929C6C57B4454B3 /* [CP] Check Pods Manifest.lock */,
				8D3360FF2B624D1500FA162D /* Sources */,
				8D3361002B624D1500FA162D /* Frameworks */,
				8D3361012B624D1500FA162D /* Resources */,
				DF75EAE11EAB0EA6923884D4 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SeeyouWidgetExtension;
			productName = SeeyouWidgetExtension;
			productReference = 8D3361032B624D1600FA162D /* SeeyouWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		8D8D7D9A2D559ECD00315CCF /* SeeyouIntents */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8D8D7DA82D559ECD00315CCF /* Build configuration list for PBXNativeTarget "SeeyouIntents" */;
			buildPhases = (
				59C6750E47BB44F38A7DA46D /* [CP] Check Pods Manifest.lock */,
				8D8D7D972D559ECD00315CCF /* Sources */,
				8D8D7D982D559ECD00315CCF /* Frameworks */,
				8D8D7D992D559ECD00315CCF /* Resources */,
				605C28CEAEEAE7B0A66774E2 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SeeyouIntents;
			productName = SeeyouIntents;
			productReference = 8D8D7D9B2D559ECD00315CCF /* SeeyouIntents.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		A9957BD71F04D81000934FF6 /* Seeyou ServiceExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A9957BE31F04D81000934FF6 /* Build configuration list for PBXNativeTarget "Seeyou ServiceExtension" */;
			buildPhases = (
				A9957BD41F04D81000934FF6 /* Sources */,
				A9957BD51F04D81000934FF6 /* Frameworks */,
				A9957BD61F04D81000934FF6 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Seeyou ServiceExtension";
			productName = "Seeyou ServiceExtension";
			productReference = A9957BD81F04D81000934FF6 /* Seeyou ServiceExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		539D0A5116FC08030092F222 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = "";
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 0510;
				ORGANIZATIONNAME = linggan;
				TargetAttributes = {
					362AC6F21D0522EF00B14848 = {
						CreatedOnToolsVersion = 7.3.1;
						DevelopmentTeam = 588AWE66S7;
						ProvisioningStyle = Manual;
						TestTargetID = 539D0A5816FC08030092F222;
					};
					3CD7D61A1DE2F2E60035CECA = {
						CreatedOnToolsVersion = 8.1;
						DevelopmentTeam = 588AWE66S7;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 1;
							};
						};
					};
					539D0A5816FC08030092F222 = {
						DevelopmentTeam = 588AWE66S7;
						LastSwiftMigration = 1160;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.AccessWiFi = {
								enabled = 1;
							};
							com.apple.ApplicationGroups.iOS = {
								enabled = 1;
							};
							com.apple.BackgroundModes = {
								enabled = 1;
							};
							com.apple.HealthKit = {
								enabled = 1;
							};
							com.apple.InAppPurchase = {
								enabled = 1;
							};
							com.apple.Keychain = {
								enabled = 1;
							};
							com.apple.Maps.iOS = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 1;
							};
							com.apple.SafariKeychain = {
								enabled = 1;
							};
							com.apple.iCloud = {
								enabled = 1;
							};
							com.apple.iOS = {
								enabled = 1;
							};
						};
					};
					8D0651642DFAAC6800271766 = {
						CreatedOnToolsVersion = 16.1;
						DevelopmentTeam = 588AWE66S7;
						ProvisioningStyle = Automatic;
					};
					8D21CB382CD4A3F100069EDC = {
						CreatedOnToolsVersion = 16.1;
						DevelopmentTeam = 588AWE66S7;
						ProvisioningStyle = Automatic;
					};
					8D3361022B624D1500FA162D = {
						CreatedOnToolsVersion = 14.3.1;
						DevelopmentTeam = 588AWE66S7;
						ProvisioningStyle = Automatic;
					};
					8D8D7D9A2D559ECD00315CCF = {
						CreatedOnToolsVersion = 16.2;
						DevelopmentTeam = 588AWE66S7;
						ProvisioningStyle = Automatic;
					};
					A9957BD71F04D81000934FF6 = {
						CreatedOnToolsVersion = 8.3.3;
						DevelopmentTeam = 588AWE66S7;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 539D0A5416FC08030092F222 /* Build configuration list for PBXProject "Seeyou" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				"zh-Hans",
				"zh-Hant",
				Base,
			);
			mainGroup = 539D0A5016FC08030092F222;
			productRefGroup = 539D0A5A16FC08030092F222 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				539D0A5816FC08030092F222 /* Seeyou */,
				362AC6F21D0522EF00B14848 /* SeeyouTests */,
				3CD7D61A1DE2F2E60035CECA /* Seeyou Today */,
				A9957BD71F04D81000934FF6 /* Seeyou ServiceExtension */,
				8D3361022B624D1500FA162D /* SeeyouWidgetExtension */,
				8D21CB382CD4A3F100069EDC /* SeeyouWatch Watch App */,
				8D8D7D9A2D559ECD00315CCF /* SeeyouIntents */,
				8D0651642DFAAC6800271766 /* SeeyouShare */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		362AC6F11D0522EF00B14848 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3CD7D6191DE2F2E60035CECA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3CD7D6231DE2F2E70035CECA /* MainInterface.storyboard in Resources */,
				3C32BACC1DE544F000C7F653 /* today-widget_search.png in Resources */,
				3C32BACF1DE544F000C7F653 /* today-widget_sign.png in Resources */,
				3C32BAC81DE544F000C7F653 /* <EMAIL> in Resources */,
				3C32BACE1DE544F000C7F653 /* <EMAIL> in Resources */,
				3C32BAC61DE544F000C7F653 /* today-widget_advise.png in Resources */,
				3C32BACB1DE544F000C7F653 /* <EMAIL> in Resources */,
				3C32BACD1DE544F000C7F653 /* <EMAIL> in Resources */,
				360AB8B42BC628FB00C30079 /* PrivacyInfo.xcprivacy in Resources */,
				3C32BACA1DE544F000C7F653 /* <EMAIL> in Resources */,
				3C32BAC71DE544F000C7F653 /* <EMAIL> in Resources */,
				3C32BAD11DE544F000C7F653 /* <EMAIL> in Resources */,
				3C32BAC91DE544F000C7F653 /* today-widget_record.png in Resources */,
				3C32BAD01DE544F000C7F653 /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		539D0A5716FC08030092F222 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				36CBC2572C23EDCB004D3030 /* <EMAIL> in Resources */,
				3673C5912DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5922DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5932DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5942DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5952DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5962DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5972DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5982DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5992DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C59A2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C59B2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C59C2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C59D2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C59E2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C59F2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A02DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A12DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A22DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A32DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A42DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A52DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A62DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A72DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A82DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5A92DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5AA2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5AB2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5AC2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5AD2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5AE2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5AF2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B02DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B12DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B22DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B32DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B42DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B52DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B62DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B72DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B82DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5B92DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5BA2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5BB2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5BC2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5BD2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5BE2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5BF2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C02DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C12DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C22DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C32DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C42DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C52DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C62DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C72DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C82DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5C92DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5CA2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5CB2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5CC2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5CD2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5CE2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5CF2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D02DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D12DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D22DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D32DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D42DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D52DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D62DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D72DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D82DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5D92DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5DA2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5DB2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5DC2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5DD2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5DE2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5DF2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E02DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E12DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E22DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E32DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E42DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E52DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E62DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E72DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E82DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5E92DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5EA2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5EB2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5EC2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5ED2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5EE2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5EF2DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F02DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F12DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F22DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F32DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F42DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F52DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F62DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F72DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F82DBDDF2000966021 /* <EMAIL> in Resources */,
				3673C5F92DBDDF2000966021 /* <EMAIL> in Resources */,
				360AB8B02BC624A100C30079 /* PrivacyInfo.xcprivacy in Resources */,
				36CBC25A2C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC25D2C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC2522C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC25C2C23EDCB004D3030 /* <EMAIL> in Resources */,
				36D6FE792BC66414005C059E /* LaunchScreenNew.storyboard in Resources */,
				36CBC2592C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC2542C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC2532C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC2512C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC25B2C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC2582C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC2502C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC2562C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC2552C23EDCB004D3030 /* <EMAIL> in Resources */,
				36CBC25E2C23EDCB004D3030 /* <EMAIL> in Resources */,
				2CA58E5A19EE20DE00B369D5 /* Images.xcassets in Resources */,
				36D177152D3609E40072042E /* <EMAIL> in Resources */,
				36D177162D3609E40072042E /* <EMAIL> in Resources */,
				36D177172D3609E40072042E /* <EMAIL> in Resources */,
				36D177182D3609E40072042E /* <EMAIL> in Resources */,
				36D177192D3609E40072042E /* <EMAIL> in Resources */,
				36D1771A2D3609E40072042E /* <EMAIL> in Resources */,
				36D1771B2D3609E40072042E /* <EMAIL> in Resources */,
				36D1771C2D3609E40072042E /* <EMAIL> in Resources */,
				36D1771D2D3609E40072042E /* <EMAIL> in Resources */,
				36D1771E2D3609E40072042E /* <EMAIL> in Resources */,
				36D1771F2D3609E40072042E /* <EMAIL> in Resources */,
				36D177202D3609E40072042E /* <EMAIL> in Resources */,
				36D177212D3609E40072042E /* <EMAIL> in Resources */,
				36D177222D3609E40072042E /* <EMAIL> in Resources */,
				36D177232D3609E40072042E /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D0651632DFAAC6800271766 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3670BF282E02AC7100239551 /* PrivacyInfo.xcprivacy in Resources */,
				3670BF262E02ABFD00239551 /* MainInterface.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D21CB372CD4A3F100069EDC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8D49717E2CDC480800D1EF0B /* PrivacyInfo.xcprivacy in Resources */,
				8DCBF8C82CD89A44008A510F /* Preview Assets.xcassets in Resources */,
				8DCBF8C92CD89A44008A510F /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D3361012B624D1500FA162D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				360AB8B62BC6294600C30079 /* PrivacyInfo.xcprivacy in Resources */,
				8D3361112B624D1900FA162D /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D8D7D992D559ECD00315CCF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				36D384462D6D58EB0022C8C8 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9957BD61F04D81000934FF6 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				360AB8B22BC628AC00C30079 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		14EB7BB19D56C3C71B0F6AA3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Seeyou-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		1CB62C40BE782E8493B2A20D /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Seeyou Today-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		1CDCD15FBC54B6AE70C44B22 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SeeyouWatch Watch App-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		36EFABD01CD3B4C800859777 /* Check Deduplication Resources  */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Seeyou/Pods-Seeyou-resources.sh",
			);
			name = "Check Deduplication Resources ";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(PODS_PODFILE_DIR_PATH)/pods_suffix.txt",
				"$(PODS_PODFILE_DIR_PATH)/pods_uniq.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#\"${PODS_PODFILE_DIR_PATH}/Pods-check-deduplication-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		380AADA38609879FE2136C51 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Seeyou/Pods-Seeyou-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5053496927B3A5D2001003D5 /* Make Cache Assets */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Make Cache Assets";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "dir=$(cd $PODS_ROOT; pwd)\nruby_file=${dir}/MeetYouAssets/assets_copy.rb\n#ruby_file='/Users/<USER>/Workspace/Meetyou/MeetYouAssets/assets_copy.rb'\necho \"ruby_file = $ruby_file\"\nif [[ -f \"$ruby_file\" ]] ; then\n    ruby $ruby_file\nfi\n";
		};
		50BAB38F257DFB5900FFDA5A /* Copy Root Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Seeyou/Pods-Seeyou-resources.sh",
			);
			name = "Copy Root Resources";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(PODS_PODFILE_DIR_PATH)/pods_root_resource.sh",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#\"${PODS_PODFILE_DIR_PATH}/Pods-copy-root-resources.sh\"\n";
		};
		59C6750E47BB44F38A7DA46D /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SeeyouIntents-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		605C28CEAEEAE7B0A66774E2 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SeeyouIntents/Pods-SeeyouIntents-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		715E433F2929C6C57B4454B3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SeeyouWidgetExtension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		AD117322B4D452DB4D2C1CB3 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SeeyouWatch Watch App/Pods-SeeyouWatch Watch App-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B92FCA5407D9C30F72DB3B4D /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Seeyou/Pods-Seeyou-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DF75EAE11EAB0EA6923884D4 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SeeyouWidgetExtension/Pods-SeeyouWidgetExtension-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		362AC6EF1D0522EF00B14848 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3CCC6AD61DEC0A3400DFAA6E /* SYSpotlightTest.m in Sources */,
				36F224F11D05302500604259 /* SeeyouTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3CD7D6171DE2F2E60035CECA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3C2A202C1DE691BB0090DA04 /* IMYTodayWidgetHelper.m in Sources */,
				3C2A20351DE698380090DA04 /* IMYTodayTopCell.m in Sources */,
				3C2A20321DE692890090DA04 /* IMYTodayBottomCell.m in Sources */,
				3CD7D6201DE2F2E70035CECA /* TodayViewController.m in Sources */,
				3C6EF1091DF06337009FF953 /* NSObject+IMYTodayWidget.m in Sources */,
				3C2A202F1DE6920B0090DA04 /* IMYTodayArticleCell.m in Sources */,
				C5F233E91F15F7BC00817CD1 /* NSDate+IMYTodayWidget.m in Sources */,
				3C6EF10C1DF0653A009FF953 /* NSData+IMYTodayWidget.m in Sources */,
				3C1F62991DF02A3A00D082B1 /* IMYTodayWidgetNetworkHelper.m in Sources */,
				3C6EF1061DF062BA009FF953 /* NSString+IMYTodayWidget.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		539D0A5516FC08030092F222 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				ED31CFD524FF6C44001ED2E4 /* SYTestViewController.swift in Sources */,
				F5C5801517139DD800CA2312 /* main.m in Sources */,
				36FB1FA8287539D3006BADB6 /* NSBundle+MYAPPINFO.m in Sources */,
				8DBD2C862E4C2D6C00F174CC /* IMYWidgetType.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D0651612DFAAC6800271766 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3670BF232E02ABFD00239551 /* IMYSharedExtension.swift in Sources */,
				3670BF242E02ABFD00239551 /* ShareViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D21CB352CD4A3F100069EDC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8DCBF8C62CD89A44008A510F /* ContentView.swift in Sources */,
				8DCBF8C72CD89A44008A510F /* SeeyouWatchApp.swift in Sources */,
				5041C3E32CDD980E00505DC2 /* ExtensionDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D3360FF2B624D1500FA162D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8DA56DC62D65943900614D75 /* IMYTimelineProvider.swift in Sources */,
				8DC803772D55B5F8004D205E /* Intents.intentdefinition in Sources */,
				8DA56DD12D65C92700614D75 /* IMYIntentTimelineProvider.swift in Sources */,
				8D3361C72B637D3100FA162D /* SmallFreeWidget01Widget.swift in Sources */,
				0E4748552E4481500092E5B5 /* MediumVipWidget03Widget.swift in Sources */,
				0E4748562E4481500092E5B5 /* SmallVipWidget01Widget.swift in Sources */,
				0E4748572E4481500092E5B5 /* SmallVipWidget02Widget.swift in Sources */,
				8D8517C52B999247004D7383 /* BabyPhotoVipWidget.swift in Sources */,
				8D33610A2B624D1700FA162D /* SeeyouWidgetBundle.swift in Sources */,
				0E4748602E4481590092E5B5 /* IMYBaByFeed02WidgetAppItents.swift in Sources */,
				0E0B9C142E49EC5B008BD3DB /* SleepActivityWidget.swift in Sources */,
				0E0B9C152E49EC5B008BD3DB /* FMCountingActivityWidget.swift in Sources */,
				0E0B9C162E49EC5B008BD3DB /* BreastMilkActivityWidget.swift in Sources */,
				8D240C082E49998500AA843A /* IMYContractionsWidget.swift in Sources */,
				0E4748612E4481590092E5B5 /* IMYBabyFeed02Widget.swift in Sources */,
				8DBD2C852E4C2D6C00F174CC /* IMYWidgetType.swift in Sources */,
				0E4748622E4481590092E5B5 /* IMYBabyFeed01Widget.swift in Sources */,
				0E4748632E4481590092E5B5 /* IMYBabyFeed03Widget.swift in Sources */,
				8D33610E2B624D1700FA162D /* SeeyouWidget.swift in Sources */,
				8DBD2C802E4C20D000F174CC /* IMYFMCountWidget.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D8D7D972D559ECD00315CCF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8DC803762D55B5F8004D205E /* Intents.intentdefinition in Sources */,
				8D8D7DAD2D559ED500315CCF /* IntentHandler.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A9957BD41F04D81000934FF6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				A9957BDC1F04D81000934FF6 /* NotificationService.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		362AC6F91D0522EF00B14848 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 539D0A5816FC08030092F222 /* Seeyou */;
			targetProxy = 362AC6F81D0522EF00B14848 /* PBXContainerItemProxy */;
		};
		3CD7D6261DE2F2E70035CECA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3CD7D61A1DE2F2E60035CECA /* Seeyou Today */;
			targetProxy = 3CD7D6251DE2F2E70035CECA /* PBXContainerItemProxy */;
		};
		8D06516E2DFAAC6900271766 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8D0651642DFAAC6800271766 /* SeeyouShare */;
			targetProxy = 8D06516D2DFAAC6900271766 /* PBXContainerItemProxy */;
		};
		8D21CB452CD4A3F300069EDC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8D21CB382CD4A3F100069EDC /* SeeyouWatch Watch App */;
			targetProxy = 8D21CB442CD4A3F300069EDC /* PBXContainerItemProxy */;
		};
		8D3361162B624D1900FA162D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8D3361022B624D1500FA162D /* SeeyouWidgetExtension */;
			targetProxy = 8D3361152B624D1900FA162D /* PBXContainerItemProxy */;
		};
		8D8D7DA32D559ECD00315CCF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8D8D7D9A2D559ECD00315CCF /* SeeyouIntents */;
			targetProxy = 8D8D7DA22D559ECD00315CCF /* PBXContainerItemProxy */;
		};
		A9957BDF1F04D81000934FF6 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A9957BD71F04D81000934FF6 /* Seeyou ServiceExtension */;
			targetProxy = A9957BDE1F04D81000934FF6 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		3670BF1F2E02ABFC00239551 /* MainInterface.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				3670BF1E2E02ABFC00239551 /* Base */,
			);
			name = MainInterface.storyboard;
			sourceTree = "<group>";
		};
		3CD7D6211DE2F2E70035CECA /* MainInterface.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				3CD7D6221DE2F2E70035CECA /* Base */,
			);
			name = MainInterface.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		362AC6FB1D0522EF00B14848 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				HEADER_SEARCH_PATHS = "${PROJECT_DIR}/../Pods/Headers/Public/**";
				INFOPLIST_FILE = SeeyouTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MTL_ENABLE_DEBUG_INFO = YES;
				PRODUCT_BUNDLE_IDENTIFIER = ljh.SeeyouTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Seeyou.app/Seeyou";
				VALID_ARCHS = "armv7 armv7s arm64 $(ARCHS_STANDARD) ";
			};
			name = Debug;
		};
		362AC6FC1D0522EF00B14848 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				HEADER_SEARCH_PATHS = "${PROJECT_DIR}/../Pods/Headers/Public/**";
				INFOPLIST_FILE = SeeyouTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = ljh.SeeyouTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Seeyou.app/Seeyou";
				VALID_ARCHS = "armv7 armv7s arm64 $(ARCHS_STANDARD) ";
			};
			name = Release;
		};
		3CD7D6281DE2F2E70035CECA /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 99560743A9297771FE5BAC99 /* Pods-Seeyou Today.debug.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = "Seeyou Today/Seeyou Today.entitlements";
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"SD_WEBP=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				INFOPLIST_FILE = "Seeyou Today/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				LLVM_LTO = YES_THIN;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.seeyouToday;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 1;
				VALID_ARCHS = "armv7 armv7s arm64 $(ARCHS_STANDARD)";
			};
			name = Debug;
		};
		3CD7D6291DE2F2E70035CECA /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CD36F4BC08BEF504F10E2A4A /* Pods-Seeyou Today.release.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = "Seeyou Today/Seeyou Today.entitlements";
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = z;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"SD_WEBP=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				INFOPLIST_FILE = "Seeyou Today/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				LLVM_LTO = YES_THIN;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.seeyouToday;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 1;
				VALID_ARCHS = "armv7 armv7s arm64 $(ARCHS_STANDARD)";
			};
			name = Release;
		};
		539D0A7A16FC08030092F222 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				ONLY_ACTIVE_ARCH = YES;
				PROVISIONING_PROFILE = "";
				"PROVISIONING_PROFILE[sdk=iphoneos*]" = "";
				SDKROOT = iphoneos;
				VALID_ARCHS = "$(ARCHS_STANDARD_64_BIT)";
			};
			name = Debug;
		};
		539D0A7B16FC08030092F222 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				PROVISIONING_PROFILE = "";
				"PROVISIONING_PROFILE[sdk=iphoneos*]" = "";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VALID_ARCHS = "$(ARCHS_STANDARD_64_BIT)";
			};
			name = Release;
		};
		539D0A7D16FC08030092F222 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1DC825B878CA0EF815FE7A92 /* Pods-Seeyou.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-Release";
				ASSETCATALOG_COMPILER_OPTIMIZATION = space;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_ENTITLEMENTS = Seeyou/Seeyou.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COMPILER_INDEX_STORE_ENABLE = NO;
				COMPRESS_PNG_FILES = YES;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEVELOPMENT_TEAM = 588AWE66S7;
				EMBEDDED_CONTENT_CONTAINS_SWIFT = NO;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)$(LOCAL_LIBRARY_DIR)",
					"$(PROJECT_DIR)/Resource",
					"$(PROJECT_DIR)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Seeyou/Seeyou-Prefix.pch";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Seeyou/TCPClient",
				);
				INFOPLIST_FILE = "Seeyou/Seeyou-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				LLVM_LTO = YES_THIN;
				MARKETING_VERSION = 8.96.0;
				ONLY_ACTIVE_ARCH = YES;
				ORDER_FILE = "${SRCROOT}/app.order";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-fobjc-arc",
					"$(inherited)",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				"PROVISIONING_PROFILE[sdk=iphoneos*]" = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				STRIP_PNG_TEXT = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Seeyou/Seeyou-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "$(inherited)";
				VALID_ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				WARNING_CFLAGS = (
					"-Wno-unused-variable",
					"-Wno-undeclared-selector",
					"-Wno-int-conversion",
					"-Wno-documentation",
					"-Wno-strict-prototypes",
					"-Wno-deprecated-declarations",
					"-Wno-nullability-completeness",
				);
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		539D0A7E16FC08030092F222 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 25389EDDBC1F5D4523833381 /* Pods-Seeyou.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-Release";
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = "";
				ASSETCATALOG_COMPILER_OPTIMIZATION = space;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_ENTITLEMENTS = Seeyou/Seeyou.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COMPILER_INDEX_STORE_ENABLE = NO;
				COMPRESS_PNG_FILES = YES;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 588AWE66S7;
				EMBEDDED_CONTENT_CONTAINS_SWIFT = NO;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)$(LOCAL_LIBRARY_DIR)",
					"$(PROJECT_DIR)/Resource",
					"$(PROJECT_DIR)",
				);
				GCC_OPTIMIZATION_LEVEL = z;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Seeyou/Seeyou-Prefix.pch";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Seeyou/TCPClient",
				);
				INFOPLIST_FILE = "Seeyou/Seeyou-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				LLVM_LTO = YES_THIN;
				MARKETING_VERSION = 8.96.0;
				ONLY_ACTIVE_ARCH = NO;
				ORDER_FILE = "${SRCROOT}/app.order";
				OTHER_LDFLAGS = (
					"-ObjC",
					"-fobjc-arc",
					"$(inherited)",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				"PROVISIONING_PROFILE[sdk=iphoneos*]" = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				STRIP_PNG_TEXT = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_BRIDGING_HEADER = "Seeyou/Seeyou-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "$(inherited)";
				VALID_ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				WARNING_CFLAGS = (
					"-Wno-unused-variable",
					"-Wno-undeclared-selector",
					"-Wno-int-conversion",
					"-Wno-documentation",
					"-Wno-strict-prototypes",
					"-Wno-deprecated-declarations",
					"-Wno-nullability-completeness",
				);
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		8D0651722DFAAC6900271766 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = SeeyouShare/SeeyouShare.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SeeyouShare/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SeeyouShare;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2025 linggan. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.SeeyouShare;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8D0651732DFAAC6900271766 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = SeeyouShare/SeeyouShare.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SeeyouShare/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SeeyouShare;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2025 linggan. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.SeeyouShare;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		8D21CB482CD4A3F300069EDC /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CD6444E2196A9F852C21005C /* Pods-SeeyouWatch Watch App.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = "SeeyouWatch Watch App/SeeyouWatchApp.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEVELOPMENT_ASSET_PATHS = "\"SeeyouWatch Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_PREVIEWS = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXCLUDED_ARCHS = armv7k;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "SeeyouWatch Watch App/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = SeeyouWatch;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = com.linggan.xiyou;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.watchApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				VALID_ARCHS = "$(ARCHS_STANDARD)";
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		8D21CB492CD4A3F300069EDC /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9CBAE9E5544A5BBFA3B2D532 /* Pods-SeeyouWatch Watch App.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = "SeeyouWatch Watch App/SeeyouWatchApp.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEVELOPMENT_ASSET_PATHS = "\"SeeyouWatch Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_PREVIEWS = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				EXCLUDED_ARCHS = armv7k;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "SeeyouWatch Watch App/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = SeeyouWatch;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = com.linggan.xiyou;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.watchApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				VALID_ARCHS = "arm64 arm64_32";
				WATCHOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
		8D3361192B624D1E00FA162D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 990FF747E3A10795158703BF /* Pods-SeeyouWidgetExtension.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = SeeyouWidget/SeeyouWidgetExtension.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SeeyouWidget/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.SeeyouWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8D33611A2B624D1E00FA162D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65AF7F4E45196A45EB0A49BE /* Pods-SeeyouWidgetExtension.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = SeeyouWidget/SeeyouWidgetExtension.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SeeyouWidget/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.SeeyouWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		8D8D7DA52D559ECD00315CCF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E33569B181AE67CC794CDA09 /* Pods-SeeyouIntents.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = SeeyouIntents/SeeyouIntents.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SeeyouIntents/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SeeyouIntents;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2025 linggan. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.SeeyouIntents;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8D8D7DA62D559ECD00315CCF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71CB06EFD380AAFACC0C7CE8 /* Pods-SeeyouIntents.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = SeeyouIntents/SeeyouIntents.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SeeyouIntents/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SeeyouIntents;
				INFOPLIST_KEY_NSHumanReadableCopyright = "Copyright © 2025 linggan. All rights reserved.";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.SeeyouIntents;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A9957BE11F04D81000934FF6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = "Seeyou ServiceExtension/Seeyou ServiceExtension.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				INFOPLIST_FILE = "Seeyou ServiceExtension/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				LLVM_LTO = YES_THIN;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.SeeyouServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "armv7 armv7s arm64 $(ARCHS_STANDARD)";
			};
			name = Debug;
		};
		A9957BE21F04D81000934FF6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CODE_SIGN_ENTITLEMENTS = "Seeyou ServiceExtension/Seeyou ServiceExtension.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=*]" = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 8.96.0;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 588AWE66S7;
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = z;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				INFOPLIST_FILE = "Seeyou ServiceExtension/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks";
				LLVM_LTO = YES_THIN;
				MARKETING_VERSION = 8.96.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.linggan.xiyou.SeeyouServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "armv7 armv7s arm64 $(ARCHS_STANDARD)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		362AC6FA1D0522EF00B14848 /* Build configuration list for PBXNativeTarget "SeeyouTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				362AC6FB1D0522EF00B14848 /* Debug */,
				362AC6FC1D0522EF00B14848 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3CD7D62A1DE2F2E70035CECA /* Build configuration list for PBXNativeTarget "Seeyou Today" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3CD7D6281DE2F2E70035CECA /* Debug */,
				3CD7D6291DE2F2E70035CECA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		539D0A5416FC08030092F222 /* Build configuration list for PBXProject "Seeyou" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				539D0A7A16FC08030092F222 /* Debug */,
				539D0A7B16FC08030092F222 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		539D0A7C16FC08030092F222 /* Build configuration list for PBXNativeTarget "Seeyou" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				539D0A7D16FC08030092F222 /* Debug */,
				539D0A7E16FC08030092F222 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8D0651712DFAAC6900271766 /* Build configuration list for PBXNativeTarget "SeeyouShare" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8D0651722DFAAC6900271766 /* Debug */,
				8D0651732DFAAC6900271766 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8D21CB472CD4A3F300069EDC /* Build configuration list for PBXNativeTarget "SeeyouWatch Watch App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8D21CB482CD4A3F300069EDC /* Debug */,
				8D21CB492CD4A3F300069EDC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8D3361182B624D1E00FA162D /* Build configuration list for PBXNativeTarget "SeeyouWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8D3361192B624D1E00FA162D /* Debug */,
				8D33611A2B624D1E00FA162D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8D8D7DA82D559ECD00315CCF /* Build configuration list for PBXNativeTarget "SeeyouIntents" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8D8D7DA52D559ECD00315CCF /* Debug */,
				8D8D7DA62D559ECD00315CCF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A9957BE31F04D81000934FF6 /* Build configuration list for PBXNativeTarget "Seeyou ServiceExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A9957BE11F04D81000934FF6 /* Debug */,
				A9957BE21F04D81000934FF6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 539D0A5116FC08030092F222 /* Project object */;
}
