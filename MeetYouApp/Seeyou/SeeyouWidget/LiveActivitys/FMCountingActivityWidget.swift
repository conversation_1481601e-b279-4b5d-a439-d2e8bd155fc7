//
//  FMCountingActivityWidget.swift
//  SeeyouWidgetExtension
//
//  Created by meetyou on 2025/8/7.
//

import WidgetKit
import SwiftUI
import SeeyouWidget
import SeeyouWidgetService

@available(iOSApplicationExtension 16.1, *)
struct FMCountingActivityWidget: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: FMCountingAttributes.self) { context in
            FMCountingLiveActivityView(context: context)
                .widgetURL(FMCountingAttributes.widgetUrl(context: context))
        } dynamicIsland: { context in
            DynamicIsland {
                DynamicIslandExpandedRegion(.leading) {
                    DynamicIslandExpandedRegionLeading(icon: context.attributes.icon, title: context.attributes.title, textColor: context.attributes.textColor)
                }
                DynamicIslandExpandedRegion(.trailing) {
                    DynamicIslandExpandedRegionTrailing()
                }
                DynamicIslandExpandedRegion(.bottom) {
                    FMCountingLiveActivityContentView(context: context, dynamicIsland: true)
                        .padding(EdgeInsets(top: 0, leading: 2, bottom: 4, trailing: 2))
                }
            } compactLeading: {
                DynamicIslandProgressFM(timerInterval: FMCountingAttributes.remainingTimer(beginTime: context.state.beginTime), textColor: context.attributes.textColor)
            } compactTrailing: {
                DynamicIslandCompactTrailing(timerInterval: FMCountingAttributes.remainingTimer(beginTime: context.state.beginTime), textColor: context.attributes.textColor, countsDown: true, isCounting: context.state.isTiming, timerString: context.attributes.remainingStr)
            } minimal: {
                DynamicIslandProgressFM(timerInterval: FMCountingAttributes.remainingTimer(beginTime: context.state.beginTime), textColor: context.attributes.textColor)
            }
            .widgetURL(FMCountingAttributes.widgetUrl(context: context, dynamicIsland: true))
            .keylineTint(.cyan)
        }
    }
}
