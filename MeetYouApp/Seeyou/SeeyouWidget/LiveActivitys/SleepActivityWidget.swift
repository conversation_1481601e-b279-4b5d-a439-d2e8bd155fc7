//
//  SleepActivityWidget.swift
//  SeeyouWidgetExtension
//
//  Created by meetyou on 2025/8/7.
//

import WidgetKit
import SwiftUI
import SeeyouWidget
import SeeyouWidgetService

@available(iOSApplicationExtension 16.1, *)
struct SleepActivityWidget: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: SleepAttributes.self) { context in
            SleepLiveActivityView(context: context)
                .widgetURL(SleepAttributes.widgetUrl(context: context))
        } dynamicIsland: { context in
            DynamicIsland {
                DynamicIslandExpandedRegion(.leading) {
                    DynamicIslandExpandedRegionLeading(icon: context.attributes.icon, title: context.attributes.title, textColor: context.attributes.textColor)
                }
                DynamicIslandExpandedRegion(.trailing) {
                    DynamicIslandExpandedRegionTrailing()
                }
                DynamicIslandExpandedRegion(.bottom) {
                    SleepLiveActivityContentView(context: context, dynamicIsland: true)
                        .padding(EdgeInsets(top: 0, leading: 2, bottom: 4, trailing: 2))
                }
            } compactLeading: {
                DynamicIslandCompactLeading(icon: context.attributes.icon)
            } compactTrailing: {
                DynamicIslandCompactTrailing(timerInterval: context.state.sleepTime, textColor: context.attributes.textColor, countsDown: false, isCounting: context.state.isTiming, timerString: context.state.sleepTimeStr)
            } minimal: {
                DynamicIslandMinimal(icon: context.attributes.icon)
            }
            .widgetURL(SleepAttributes.widgetUrl(context: context, dynamicIsland: true))
            .keylineTint(.cyan)
        }
    }
}

