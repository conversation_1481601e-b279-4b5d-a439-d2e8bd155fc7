//
//  IMYFMCountWidget.swift
//  SeeyouWidgetExtension
//
//  Created by meetyou on 2025/8/13.
//  Copyright © 2025 linggan. All rights reserved.
//

import WidgetKit
import SwiftUI
import SeeyouWidget
import SeeyouWidgetService

struct IMYFMCountWidgetProvider: IMYTimelineProvider {
    typealias ProviderHandler = IMYFMCountWidgetProviderHandler
    typealias Entry = ProviderHandler.Entry
    var handler: ProviderHandler = ProviderHandler.init()
}

struct IMYFMCountWidget: Widget {
    
    static let type:IMYPregnantType = IMYPregnantType.fmCount
    
    let kind: String = type.rawValue
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: IMYFMCountWidgetProvider()) { entry in
            if entry.enable {
                IMYFMCountView(entry: entry)
            } else {
                IMYFMCountNoPermissionView()
            }
        }
        .configurationDisplayName(Self.type.displayName)
        .description(Self.type.description)
        .supportedFamilies(Self.type.families)
        .disableContentMarginsIfNeeded()
    }
}
