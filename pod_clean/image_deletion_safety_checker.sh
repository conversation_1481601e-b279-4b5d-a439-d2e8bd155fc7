#!/bin/bash

# 图片资源删除安全检查脚本
# 版本: v1.0
# 功能: 检查 Git 工作区中被删除的图片资源文件，进行引用检查并提供交互式确认
# 
# 主要功能:
#   1. 自动切换到各个 Pod 项目目录
#   2. 检查 Git 状态中被删除的图片文件
#   3. 对删除的图片进行引用检查（直接引用、模糊引用、字符串引用等）
#   4. 交互式提示用户确认删除
#   5. 提供安全措施和批量处理选项
#
# 用法:
#   ./image_deletion_safety_checker.sh              # 交互式检查所有 Pod
#   ./image_deletion_safety_checker.sh -p <pod>     # 检查指定 Pod
#   ./image_deletion_safety_checker.sh -a           # 自动模式（跳过交互）
#   ./image_deletion_safety_checker.sh -h           # 显示帮助

set -e

# 信号处理 - 优雅退出
cleanup() {
    echo ""
    echo "🛑 检查被中断，正在清理..."
    exit 130
}

trap cleanup SIGINT SIGTERM

# 脚本目录配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="$SCRIPT_DIR/scripts"
LIB_DIR="$SCRIPTS_DIR/lib"

# 加载核心库
source "$LIB_DIR/common.sh"
source "$LIB_DIR/database.sh"

# 如果库加载失败，提供基础日志函数
if ! declare -f log_info > /dev/null 2>&1; then
    log_info() { echo "[INFO] $1"; }
    log_success() { echo "[SUCCESS] $1"; }
    log_warning() { echo "[WARNING] $1"; }
    log_error() { echo "[ERROR] $1"; }
    log_header() { echo "[HEADER] $1"; }
fi

# 全局配置
AUTO_MODE=false
TARGET_POD=""
VERBOSE=false
DB_FIRST_MODE=false
PARALLEL_MODE=false
MAX_PARALLEL_JOBS=4
LOG_DIR="logs"
CURRENT_LOG_FILE=""

# 支持的图片格式
IMAGE_EXTENSIONS=("png" "jpg" "jpeg" "gif" "svg" "webp" "bmp" "tiff" "ico")

# 获取已删除的记录 - 从数据库读取删除记录
get_deleted_records_from_db() {
    local pod_name="$1"
    local deleted_records=()

    if [ ! -f "$DB_FILE" ]; then
        log_warning "数据库文件不存在，无法获取删除记录"
        return 1
    fi

    # 查询指定Pod的删除记录
    local query_result
    if [ -n "$pod_name" ]; then
        query_result=$(sqlite3 "$DB_FILE" "SELECT file_path, status, deletion_time FROM deletion_progress WHERE pod_name='$pod_name' ORDER BY deletion_time DESC;" 2>/dev/null)
    else
        query_result=$(sqlite3 "$DB_FILE" "SELECT pod_name, file_path, status, deletion_time FROM deletion_progress ORDER BY deletion_time DESC;" 2>/dev/null)
    fi

    if [ -z "$query_result" ]; then
        log_info "未找到删除记录"
        return 0
    fi

    echo "$query_result"
}

# 显示删除记录摘要
show_deletion_records_summary() {
    local pod_name="$1"

    log_header "删除记录摘要"

    if [ ! -f "$DB_FILE" ]; then
        log_warning "数据库文件不存在"
        return 1
    fi

    if [ -n "$pod_name" ]; then
        log_info "Pod: $pod_name 的删除记录"
        sqlite3 -header -column "$DB_FILE" << EOF
SELECT
    file_path as "文件路径",
    status as "状态",
    datetime(deletion_time, 'localtime') as "删除时间",
    user_id as "操作用户"
FROM deletion_progress
WHERE pod_name='$pod_name'
ORDER BY deletion_time DESC
LIMIT 20;
EOF
    else
        log_info "所有Pod的删除记录摘要"
        sqlite3 -header -column "$DB_FILE" << 'EOF'
SELECT
    pod_name as "Pod库",
    COUNT(*) as "总删除数",
    SUM(CASE WHEN status='completed' THEN 1 ELSE 0 END) as "已完成",
    SUM(CASE WHEN status='pending' THEN 1 ELSE 0 END) as "待处理",
    MAX(datetime(deletion_time, 'localtime')) as "最近删除时间"
FROM deletion_progress
GROUP BY pod_name
ORDER BY pod_name;
EOF
    fi
}

# 检查文件是否在删除记录中
is_file_in_deletion_records() {
    local pod_name="$1"
    local file_path="$2"

    if [ ! -f "$DB_FILE" ]; then
        return 1
    fi

    local count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM deletion_progress WHERE pod_name='$pod_name' AND file_path='$file_path';" 2>/dev/null || echo "0")
    [ "$count" -gt 0 ]
}

# 获取文件的删除状态
get_file_deletion_status() {
    local pod_name="$1"
    local file_path="$2"

    if [ ! -f "$DB_FILE" ]; then
        echo "unknown"
        return
    fi

    local status=$(sqlite3 "$DB_FILE" "SELECT status FROM deletion_progress WHERE pod_name='$pod_name' AND file_path='$file_path';" 2>/dev/null)
    echo "${status:-unknown}"
}

# 初始化日志系统
init_logging() {
    local pod_name="$1"

    # 创建日志目录
    mkdir -p "$LOG_DIR"

    # 生成日志文件名
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    CURRENT_LOG_FILE="$LOG_DIR/regression_check_${pod_name}_${timestamp}.log"

    # 写入日志头部
    {
        echo "=================================="
        echo "回归检查日志"
        echo "Pod项目: $pod_name"
        echo "检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "检查模式: $([ "$PARALLEL_MODE" = "true" ] && echo "并行模式" || echo "串行模式")"
        echo "并行任务数: $MAX_PARALLEL_JOBS"
        echo "=================================="
        echo ""
    } > "$CURRENT_LOG_FILE"

    log_info "日志文件: $CURRENT_LOG_FILE"
}

# 记录回归检查日志
log_regression_check() {
    local message="$1"

    if [ -n "$CURRENT_LOG_FILE" ] && [ -f "$(dirname "$CURRENT_LOG_FILE")" ]; then
        echo "[$(date '+%H:%M:%S')] $message" >> "$CURRENT_LOG_FILE" 2>/dev/null || true
    fi
}

# 显示回归检查日志
show_regression_logs() {
    local pod_name="$1"

    echo ""
    log_header "回归检查日志"

    if [ -n "$pod_name" ]; then
        # 显示指定Pod的最新日志
        local latest_log=$(ls -t "$LOG_DIR"/regression_check_${pod_name}_*.log 2>/dev/null | head -1)
        if [ -n "$latest_log" ]; then
            log_info "显示 $pod_name 的最新回归检查日志:"
            echo ""
            cat "$latest_log"
        else
            log_warning "未找到 $pod_name 的回归检查日志"
        fi
    else
        # 显示所有可用的日志文件
        log_info "可用的回归检查日志文件:"
        if ls "$LOG_DIR"/regression_check_*.log >/dev/null 2>&1; then
            for log_file in "$LOG_DIR"/regression_check_*.log; do
                local basename=$(basename "$log_file")
                local file_size=$(du -h "$log_file" | cut -f1)
                local mod_time=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M" "$log_file" 2>/dev/null || stat -c "%y" "$log_file" 2>/dev/null | cut -d' ' -f1-2)
                echo "  📄 $basename ($file_size, $mod_time)"
            done
            echo ""
            echo "使用以下命令查看具体日志:"
            echo "  cat $LOG_DIR/regression_check_<pod_name>_<timestamp>.log"
        else
            log_warning "未找到任何回归检查日志文件"
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "图片资源删除安全检查脚本 v2.0"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -p, --pod <名称>    检查指定的 Pod 项目"
    echo "  -a, --auto          自动模式（跳过交互式确认）"
    echo "  -v, --verbose       详细输出模式"
    echo "  -r, --records       显示删除记录摘要"
    echo "  --check-db          检查数据库中的删除记录与当前Git状态"
    echo "  --db-first          优先从数据库记录进行回归检查"
    echo "  --parallel          启用并行处理模式（提高检查速度）"
    echo "  -j, --jobs <数量>   并行任务数量（默认: 4）"
    echo ""
    echo "功能说明:"
    echo "  - 检查 Git 工作区中被删除的图片文件"
    echo "  - 从数据库获取已删除的记录进行安全检查"
    echo "  - 进行多种类型的引用检查（直接引用、模糊匹配、字符串引用等）"
    echo "  - 验证删除操作的合理性，检查误删风险"
    echo "  - 提供交互式确认，防止误删重要资源"
    echo "  - 支持批量处理和安全回滚"
    echo "  - 支持并行处理以提高检查速度"
    echo ""
    echo "示例:"
    echo "  $0                  # 交互式检查所有 Pod"
    echo "  $0 -p MyPod         # 只检查 MyPod 项目"
    echo "  $0 -a               # 自动模式，不需要交互确认"
    echo "  $0 -r               # 显示删除记录摘要"
    echo "  $0 --check-db       # 检查数据库记录与Git状态的一致性"
    echo "  $0 --db-first       # 优先从数据库记录进行回归检查"
    echo "  $0 --db-first --parallel -j 8  # 并行回归检查（8个任务）"
}

# 检查是否为图片文件
is_image_file() {
    local file="$1"
    local ext=$(echo "${file##*.}" | tr '[:upper:]' '[:lower:]')
    
    for img_ext in "${IMAGE_EXTENSIONS[@]}"; do
        if [ "$ext" = "$img_ext" ]; then
            return 0
        fi
    done
    return 1
}

# 获取 Git 中被删除的图片文件
get_deleted_images() {
    local pod_dir="$1"
    local deleted_images=()

    # 检查工作区中被删除的文件
    while IFS= read -r line; do
        # Git status 输出格式: " D filename" 或 "D  filename"
        if [[ "$line" =~ ^[[:space:]]*D[[:space:]]+(.+)$ ]]; then
            local file="${BASH_REMATCH[1]}"
            # 跳过空文件名
            if [ -n "$file" ] && is_image_file "$file"; then
                deleted_images+=("$file")
            fi
        fi
    done < <(git status --porcelain 2>/dev/null || true)

    # 检查暂存区中被删除的文件
    while IFS= read -r line; do
        if [[ "$line" =~ ^D[[:space:]]+(.+)$ ]]; then
            local file="${BASH_REMATCH[1]}"
            # 跳过空文件名
            if [ -n "$file" ] && is_image_file "$file"; then
                # 避免重复添加
                local already_added=false
                for existing in "${deleted_images[@]}"; do
                    if [ "$existing" = "$file" ]; then
                        already_added=true
                        break
                    fi
                done
                if [ "$already_added" = false ]; then
                    deleted_images+=("$file")
                fi
            fi
        fi
    done < <(git diff --cached --name-status 2>/dev/null || true)

    # 过滤掉空字符串
    local filtered_images=()
    for img in "${deleted_images[@]}"; do
        if [ -n "$img" ]; then
            filtered_images+=("$img")
        fi
    done

    printf '%s\n' "${filtered_images[@]}"
}

# 回归检查 - 更宽松的引用检查（兜底检查）
check_file_references_regression() {
    local file="$1"
    local pod_dir="$2"
    local references=()

    # 跳过空文件名
    if [ -z "$file" ]; then
        return 0
    fi

    local filename=$(basename "$file")
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')
    local clean_name=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//' | sed 's/_normal$//' | sed 's/_selected$//' | sed 's/_hover$//' | sed 's/_pressed$//' | sed 's/_disabled$//' | sed 's/_highlighted$//')

    # 回归检查输出和日志记录
    local check_msg="🔍 回归检查文件: $filename"
    echo "    $check_msg"
    log_regression_check "$check_msg"

    local extract_msg="📝 提取关键词: $basename_no_ext -> $clean_name"
    echo "    $extract_msg"
    log_regression_check "$extract_msg"

    # 生成搜索模式 - 优化版，避免重复和过短模式
    local search_patterns=()
    local added_patterns=()  # 用于去重

    # 添加模式的辅助函数，自动去重和长度检查
    add_pattern() {
        local pattern="$1"
        if [ -n "$pattern" ] && [ ${#pattern} -gt 3 ]; then  # 最小长度限制
            # 检查是否已存在
            local exists=false
            for existing in "${added_patterns[@]}"; do
                if [ "$existing" = "$pattern" ]; then
                    exists=true
                    break
                fi
            done
            if [ "$exists" = false ]; then
                search_patterns+=("$pattern")
                added_patterns+=("$pattern")
            fi
        fi
    }

    # 1. 完整文件名
    if [ -n "$filename" ]; then
        add_pattern "$filename"
    fi

    # 2. 不含扩展名
    if [ -n "$basename_no_ext" ]; then
        add_pattern "$basename_no_ext"
    fi

    # 3. 清理后的名称
    if [ -n "$clean_name" ]; then
        add_pattern "$clean_name"
        add_pattern "${clean_name}_"  # 带下划线的前缀
    fi

    # 4. 对于 iOS 图片，添加无分辨率后缀的基础名称
    if [ -n "$basename_no_ext" ]; then
        local base_name_for_ios=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//')
        if [ "$base_name_for_ios" != "$basename_no_ext" ]; then
            add_pattern "$base_name_for_ios"
        fi
    fi

    # 5. 只保留主要单词（去掉常见后缀）
    if [ -n "$clean_name" ]; then
        local core_name=$(echo "$clean_name" | sed 's/_\(icon\|img\|image\|pic\|picture\|btn\|button\|bg\|background\)$//' | sed 's/^\(icon\|img\|image\|pic\|picture\|btn\|button\|bg\|background\)_//')
        if [ "$core_name" != "$clean_name" ]; then
            add_pattern "$core_name"
        fi
    fi

    # 如果没有有效的搜索模式，直接返回
    if [ ${#search_patterns[@]} -eq 0 ]; then
        echo "    ❌ 无有效搜索模式"
        return 0
    fi

    local patterns_msg="🎯 生成搜索模式: ${#search_patterns[@]} 个"
    echo "    $patterns_msg"
    log_regression_check "$patterns_msg"

    for pattern in "${search_patterns[@]}"; do
        local pattern_msg="- '$pattern'"
        echo "       $pattern_msg"
        log_regression_check "  $pattern_msg"
    done

    # 确定搜索工具和参数
    local search_cmd="grep"
    local search_args="-r -l -i"  # 递归、只显示文件名、忽略大小写
    local file_includes="--include=*.m --include=*.mm --include=*.h --include=*.swift --include=*.xib --include=*.storyboard --include=*.json"

    if command -v rg >/dev/null 2>&1; then
        search_cmd="rg"
        search_args="-l -i"  # 只显示文件名、忽略大小写
        file_includes="-t objc -t objcpp -t swift -t json"
    fi

    # 执行搜索
    for pattern in "${search_patterns[@]}"; do
        # 跳过空模式
        if [ -z "$pattern" ]; then
            continue
        fi

        local found_files=()

        local search_msg="🔎 搜索模式: '$pattern'"
        echo "    $search_msg"
        log_regression_check "$search_msg"

        if [ "$search_cmd" = "rg" ]; then
            while IFS= read -r ref_file; do
                if [ -n "$ref_file" ]; then
                    found_files+=("$ref_file")
                fi
            done < <(rg -l -i -t objc -t objcpp -t swift -t json "$pattern" "$pod_dir" 2>/dev/null || true)
        else
            while IFS= read -r ref_file; do
                if [ -n "$ref_file" ]; then
                    found_files+=("$ref_file")
                fi
            done < <(cd "$pod_dir" && grep -r -l -i --include="*.m" --include="*.mm" --include="*.h" --include="*.swift" --include="*.xib" --include="*.storyboard" --include="*.json" --include="*.plist" --exclude="Contents.json" "$pattern" . 2>/dev/null || true)
        fi

        if [ ${#found_files[@]} -gt 0 ]; then
            local files_str=$(printf '%s ' "${found_files[@]}")
            references+=("回归模式:'$pattern' -> $files_str")
            local found_msg="✅ 找到 ${#found_files[@]} 个引用文件"
            echo "       $found_msg"
            log_regression_check "  $found_msg"
            log_regression_check "    引用文件: $files_str"
        else
            local not_found_msg="❌ 未找到引用"
            echo "       $not_found_msg"
            log_regression_check "  $not_found_msg"
        fi
    done

    # 显示回归检查总结
    if [ ${#references[@]} -gt 0 ]; then
        local result_msg="📊 回归检查结果: 发现 ${#references[@]} 个引用模式匹配"
        echo "    $result_msg"
        log_regression_check "$result_msg"
    else
        local result_msg="📊 回归检查结果: 未发现任何引用，确认可安全删除"
        echo "    $result_msg"
        log_regression_check "$result_msg"
    fi

    printf '%s\n' "${references[@]}"
}

# 检查文件引用 - 多种匹配模式（原有的严格检查）
check_file_references() {
    local file="$1"
    local pod_dir="$2"
    local references=()

    # 跳过空文件名
    if [ -z "$file" ]; then
        return 0
    fi

    local filename=$(basename "$file")
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')
    local clean_name=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//' | sed 's/_normal$//' | sed 's/_selected$//' | sed 's/_hover$//' | sed 's/_pressed$//' | sed 's/_disabled$//' | sed 's/_highlighted$//')

    # 调试输出
    if [ "$VERBOSE" = "true" ]; then
        echo "    [DEBUG] 检查文件: $filename"
        echo "    [DEBUG] 基础名称: $basename_no_ext"
        echo "    [DEBUG] 清理名称: $clean_name"
    fi
    
    # 搜索模式数组 - 跳过空模式
    local search_patterns=()
    if [ -n "$filename" ]; then
        search_patterns+=("$filename")                    # 完整文件名
    fi
    if [ -n "$basename_no_ext" ]; then
        search_patterns+=("$basename_no_ext")            # 不含扩展名
    fi
    if [ -n "$clean_name" ]; then
        search_patterns+=("$clean_name")                 # 清理后的名称
        search_patterns+=("${clean_name}_")              # 带下划线的前缀
    fi
    
    # 对于 iOS 图片，添加无分辨率后缀的基础名称
    if [ -n "$basename_no_ext" ]; then
        local base_name_for_ios=$(echo "$basename_no_ext" | sed 's/@[0-9]x$//')
        if [ "$base_name_for_ios" != "$basename_no_ext" ] && [ -n "$base_name_for_ios" ]; then
            search_patterns+=("$base_name_for_ios")
        fi
    fi

    # 如果没有有效的搜索模式，直接返回
    if [ ${#search_patterns[@]} -eq 0 ]; then
        return 0
    fi
    
    # 选择搜索工具
    local search_cmd="grep"
    local search_args="-r -l"
    
    if command -v rg >/dev/null 2>&1; then
        search_cmd="rg"
        search_args="--files-with-matches"
    fi
    
    # 文件类型过滤
    local file_includes=""
    if [ "$search_cmd" = "grep" ]; then
        file_includes="--include=*.m --include=*.h --include=*.swift --include=*.mm --include=*.xib --include=*.storyboard --include=*.plist --include=*.json --include=*.strings --include=*.xml --exclude=Contents.json"
    else
        file_includes="-t c -t cpp -t js -t ts -t py -t java -t swift -t objc -t json -t xml -t yaml"
    fi
    
    # 执行搜索
    for pattern in "${search_patterns[@]}"; do
        # 跳过空模式
        if [ -z "$pattern" ]; then
            continue
        fi

        local found_files=()

        if [ "$VERBOSE" = "true" ]; then
            echo "    [DEBUG] 搜索模式: $pattern"
        fi

        if [ "$search_cmd" = "rg" ]; then
            while IFS= read -r ref_file; do
                if [ -n "$ref_file" ]; then
                    found_files+=("$ref_file")
                fi
            done < <(rg $search_args $file_includes --glob '*.plist' --glob '!Contents.json' "$pattern" "$pod_dir" 2>/dev/null || true)
        else
            while IFS= read -r ref_file; do
                if [ -n "$ref_file" ]; then
                    found_files+=("$ref_file")
                fi
            done < <($search_cmd $search_args $file_includes "$pattern" "$pod_dir" 2>/dev/null || true)
        fi

        if [ ${#found_files[@]} -gt 0 ]; then
            local files_str=$(printf '%s ' "${found_files[@]}")
            references+=("模式:'$pattern' -> $files_str")

            if [ "$VERBOSE" = "true" ]; then
                echo "    [DEBUG] 找到 ${#found_files[@]} 个引用文件"
            fi
        fi
    done
    
    # 特殊检查：Asset Catalog 引用
    local file_dir=$(dirname "$file")
    if [[ "$file_dir" == *.imageset ]]; then
        local imageset_name=$(basename "$file_dir" .imageset)
        local asset_refs=()
        
        if [ "$search_cmd" = "rg" ]; then
            while IFS= read -r ref_file; do
                asset_refs+=("$ref_file")
            done < <(rg --files-with-matches -t c -t cpp -t swift -t objc --glob '!Contents.json' "$imageset_name" "$pod_dir" 2>/dev/null || true)
        else
            while IFS= read -r ref_file; do
                asset_refs+=("$ref_file")
            done < <(grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" --include="*.xib" --include="*.storyboard" --exclude="Contents.json" "$imageset_name" "$pod_dir" 2>/dev/null || true)
        fi
        
        if [ ${#asset_refs[@]} -gt 0 ]; then
            local asset_files_str=$(printf '%s ' "${asset_refs[@]}")
            references+=("Asset Catalog:'$imageset_name' -> $asset_files_str")
        fi
    fi
    
    printf '%s\n' "${references[@]}"
}

# 显示引用详情（增强版 - 包含删除记录检查）
show_reference_details() {
    local file="$1"
    local pod_dir="$2"
    local references=("${@:3}")
    local pod_name=$(basename "$pod_dir")

    echo ""
    echo "🔍 文件: $file"
    echo "📁 位置: $pod_dir"

    # 检查删除记录状态
    if is_file_in_deletion_records "$pod_name" "$file"; then
        local deletion_status=$(get_file_deletion_status "$pod_name" "$file")
        case "$deletion_status" in
            "completed")
                echo "📋 删除记录: ✅ 已在数据库中标记为已删除"
                ;;
            "pending")
                echo "📋 删除记录: ⏳ 在数据库中标记为待删除"
                ;;
            *)
                echo "📋 删除记录: ❓ 状态未知 ($deletion_status)"
                ;;
        esac
    else
        echo "📋 删除记录: ❌ 未在数据库中找到删除记录"
        log_warning "文件 $file 未在删除记录中，可能是手动删除或意外删除"
    fi
    echo ""

    if [ ${#references[@]} -eq 0 ]; then
        echo "✅ 未发现引用，可能可以安全删除"
        return 0
    else
        echo "⚠️  发现 ${#references[@]} 个潜在引用:"
        for ref in "${references[@]}"; do
            if [ -n "$ref" ]; then
                echo "   • $ref"
            fi
        done
        return 1
    fi
}

# 交互式确认删除（增强版 - 包含删除记录信息）
interactive_confirm_deletion() {
    local file="$1"
    local pod_dir="$2"
    local has_references="$3"
    local pod_name=$(basename "$pod_dir")

    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    # 显示删除记录状态
    if is_file_in_deletion_records "$pod_name" "$file"; then
        local deletion_status=$(get_file_deletion_status "$pod_name" "$file")
        case "$deletion_status" in
            "completed")
                echo "📋 此文件已在数据库中标记为已删除 ✅"
                ;;
            "pending")
                echo "📋 此文件在数据库中标记为待删除 ⏳"
                ;;
        esac
    else
        echo "⚠️  警告: 此文件未在删除记录中，可能是意外删除！"
    fi

    if [ "$has_references" = "true" ]; then
        echo "⚠️  警告: 发现潜在引用，删除可能导致问题！"
    else
        echo "✅ 该文件似乎没有被引用"
    fi

    echo ""
    echo "请选择操作:"
    echo "1. 确认删除此文件"
    echo "2. 跳过此文件（保留）"
    echo "3. 查看更多引用详情"
    echo "4. 在编辑器中打开引用文件"
    echo "5. 恢复此文件（git checkout）"
    echo "6. 记录到删除数据库"
    echo "7. 批量处理剩余文件"
    echo "8. 退出检查"
    echo ""
    echo -n "请选择 (1-8): "

    local choice
    read -r choice

    case "$choice" in
        1)
            return 0  # 确认删除
            ;;
        2)
            return 1  # 跳过
            ;;
        3)
            show_detailed_references "$file" "$pod_dir"
            interactive_confirm_deletion "$file" "$pod_dir" "$has_references"
            ;;
        4)
            open_reference_files "$file" "$pod_dir"
            interactive_confirm_deletion "$file" "$pod_dir" "$has_references"
            ;;
        5)
            restore_file "$file" "$pod_dir"
            return 2  # 已恢复
            ;;
        6)
            record_file_to_deletion_db "$file" "$pod_dir"
            interactive_confirm_deletion "$file" "$pod_dir" "$has_references"
            ;;
        7)
            return 3  # 批量处理
            ;;
        8)
            return 4  # 退出
            ;;
        *)
            echo "无效选择，请重新输入"
            interactive_confirm_deletion "$file" "$pod_dir" "$has_references"
            ;;
    esac
}

# 显示详细引用信息
show_detailed_references() {
    local file="$1"
    local pod_dir="$2"

    echo ""
    echo "🔍 详细引用分析:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    local filename=$(basename "$file")
    local basename_no_ext=$(echo "$filename" | sed 's/\.[^.]*$//')

    # 使用 grep 显示具体的引用行
    echo "搜索模式: $filename"
    if command -v rg >/dev/null 2>&1; then
        rg -n --color=always -C 2 "$filename" "$pod_dir" 2>/dev/null || echo "  未找到直接引用"
    else
        grep -rn --color=always -C 2 --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" "$filename" "$pod_dir" 2>/dev/null || echo "  未找到直接引用"
    fi

    echo ""
    echo "搜索模式: $basename_no_ext"
    if command -v rg >/dev/null 2>&1; then
        rg -n --color=always -C 2 "$basename_no_ext" "$pod_dir" 2>/dev/null || echo "  未找到基础名称引用"
    else
        grep -rn --color=always -C 2 --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" "$basename_no_ext" "$pod_dir" 2>/dev/null || echo "  未找到基础名称引用"
    fi

    echo ""
    echo "按任意键继续..."
    read -n 1 -s
}

# 在编辑器中打开引用文件
open_reference_files() {
    local file="$1"
    local pod_dir="$2"

    local filename=$(basename "$file")
    local ref_files=()

    # 查找引用文件
    if command -v rg >/dev/null 2>&1; then
        while IFS= read -r ref_file; do
            ref_files+=("$ref_file")
        done < <(rg --files-with-matches -t c -t cpp -t swift -t objc "$filename" "$pod_dir" 2>/dev/null | head -5)
    else
        while IFS= read -r ref_file; do
            ref_files+=("$ref_file")
        done < <(grep -r -l --include="*.m" --include="*.h" --include="*.swift" --include="*.mm" "$filename" "$pod_dir" 2>/dev/null | head -5)
    fi

    if [ ${#ref_files[@]} -eq 0 ]; then
        echo "未找到引用文件"
        return
    fi

    echo "找到 ${#ref_files[@]} 个引用文件:"
    for i in "${!ref_files[@]}"; do
        echo "$((i+1)). ${ref_files[i]}"
    done

    echo ""
    echo -n "选择要打开的文件编号 (1-${#ref_files[@]}), 或按 Enter 跳过: "
    local choice
    read -r choice

    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#ref_files[@]} ]; then
        local selected_file="${ref_files[$((choice-1))]}"
        echo "正在打开: $selected_file"

        # 尝试使用不同的编辑器
        if command -v code >/dev/null 2>&1; then
            code "$selected_file"
        elif command -v subl >/dev/null 2>&1; then
            subl "$selected_file"
        elif command -v vim >/dev/null 2>&1; then
            vim "$selected_file"
        else
            echo "未找到可用的编辑器，文件路径: $selected_file"
        fi
    fi
}

# 记录文件到删除数据库
record_file_to_deletion_db() {
    local file="$1"
    local pod_dir="$2"
    local pod_name=$(basename "$pod_dir")

    log_info "记录文件到删除数据库: $file"
    record_deletion_candidate "$pod_name" "$file"
    log_success "已记录到删除数据库"
}

# 恢复文件
restore_file() {
    local file="$1"
    local pod_dir="$2"

    echo "正在恢复文件: $file"
    if git checkout -- "$file" 2>/dev/null; then
        log_success "文件已恢复: $file"
    else
        log_error "恢复文件失败: $file"
    fi
}

# 批量处理模式选择（增强版）
batch_processing_mode() {
    echo ""
    echo "批量处理选项:"
    echo "1. 删除所有无引用的文件"
    echo "2. 删除所有文件（忽略引用警告）"
    echo "3. 跳过所有剩余文件"
    echo "4. 恢复所有剩余文件"
    echo "5. 记录所有文件到删除数据库"
    echo "6. 返回逐个确认模式"
    echo ""
    echo -n "请选择 (1-6): "

    local choice
    read -r choice

    case "$choice" in
        1) echo "delete_safe" ;;
        2) echo "delete_all" ;;
        3) echo "skip_all" ;;
        4) echo "restore_all" ;;
        5) echo "record_all" ;;
        6) echo "continue" ;;
        *)
            echo "无效选择，返回逐个确认模式"
            echo "continue"
            ;;
    esac
}

# 检查单个 Pod 项目
check_pod_project() {
    local pod_dir="$1"
    local pod_name=$(basename "$pod_dir")

    log_header "检查 Pod 项目: $pod_name"
    echo "目录: $pod_dir"

    # 切换到 Pod 目录
    if ! cd "$pod_dir" 2>/dev/null; then
        log_error "无法进入目录: $pod_dir"
        return 1
    fi

    # 检查是否为 Git 仓库
    if ! git rev-parse --git-dir >/dev/null 2>&1; then
        log_warning "不是 Git 仓库，跳过: $pod_dir"
        return 0
    fi

    # 获取被删除的图片文件
    local deleted_images=()
    while IFS= read -r img; do
        deleted_images+=("$img")
    done < <(get_deleted_images "$pod_dir")

    if [ ${#deleted_images[@]} -eq 0 ]; then
        log_info "未发现被删除的图片文件"
        return 0
    fi

    log_info "发现 ${#deleted_images[@]} 个被删除的图片文件"

    # 统计变量
    local total_files=${#deleted_images[@]}
    local processed=0
    local confirmed_deletions=0
    local skipped_files=0
    local restored_files=0
    local batch_mode=""

    # 逐个检查文件
    for img_file in "${deleted_images[@]}"; do
        processed=$((processed + 1))

        echo ""
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "进度: $processed/$total_files"

        # 检查文件引用
        local references=()
        while IFS= read -r ref; do
            references+=("$ref")
        done < <(check_file_references "$img_file" "$pod_dir")

        local has_references="false"
        if [ ${#references[@]} -gt 0 ]; then
            has_references="true"
        fi

        # 显示引用详情
        show_reference_details "$img_file" "$pod_dir" "${references[@]}"

        # 根据批量模式处理
        local action=""
        case "$batch_mode" in
            "delete_safe")
                if [ "$has_references" = "false" ]; then
                    action="delete"
                else
                    action="skip"
                    echo "⚠️  批量安全模式: 跳过有引用的文件"
                fi
                ;;
            "delete_all")
                action="delete"
                echo "⚠️  批量删除模式: 强制删除（忽略引用）"
                ;;
            "skip_all")
                action="skip"
                echo "ℹ️  批量跳过模式: 保留文件"
                ;;
            "restore_all")
                action="restore"
                echo "🔄 批量恢复模式: 恢复文件"
                ;;
            "record_all")
                action="record"
                echo "📋 批量记录模式: 记录到删除数据库"
                ;;
            *)
                # 交互式确认
                if [ "$AUTO_MODE" = "true" ]; then
                    if [ "$has_references" = "false" ]; then
                        action="delete"
                        echo "🤖 自动模式: 删除无引用文件"
                    else
                        action="skip"
                        echo "🤖 自动模式: 跳过有引用文件"
                    fi
                else
                    local confirm_result
                    interactive_confirm_deletion "$img_file" "$pod_dir" "$has_references"
                    confirm_result=$?

                    case $confirm_result in
                        0) action="delete" ;;
                        1) action="skip" ;;
                        2) action="restore" ;;
                        3)
                            batch_mode=$(batch_processing_mode)
                            if [ "$batch_mode" = "continue" ]; then
                                batch_mode=""
                                # 重新处理当前文件
                                processed=$((processed - 1))
                                continue
                            else
                                # 重新处理当前文件
                                processed=$((processed - 1))
                                continue
                            fi
                            ;;
                        4)
                            log_info "用户选择退出检查"
                            break
                            ;;
                    esac
                fi
                ;;
        esac

        # 执行操作
        case "$action" in
            "delete")
                echo "🗑️  确认删除: $img_file"
                confirmed_deletions=$((confirmed_deletions + 1))
                # 标记为已删除
                mark_deletion_completed "$pod_name" "$img_file"
                ;;
            "skip")
                echo "⏭️  跳过文件: $img_file"
                skipped_files=$((skipped_files + 1))
                ;;
            "restore")
                restore_file "$img_file" "$pod_dir"
                restored_files=$((restored_files + 1))
                ;;
            "record")
                record_file_to_deletion_db "$img_file" "$pod_dir"
                skipped_files=$((skipped_files + 1))
                ;;
        esac

        # 在非自动模式下，给用户一点时间查看结果
        if [ "$AUTO_MODE" = "false" ] && [ -z "$batch_mode" ]; then
            sleep 0.5
        fi
    done

    # 显示总结
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_header "Pod $pod_name 检查完成"
    echo "总计处理: $total_files 个文件"
    echo "确认删除: $confirmed_deletions 个"
    echo "跳过保留: $skipped_files 个"
    echo "恢复文件: $restored_files 个"

    if [ $confirmed_deletions -gt 0 ]; then
        log_warning "注意: 脚本仅进行检查，实际删除需要手动执行 git 操作"
        echo "要完成删除，请运行: git add -A && git commit -m '删除无引用图片资源'"
    fi
}

# 发现所有 Pod 项目
discover_pod_projects() {
    local pod_dirs=()

    # 在上级目录中查找 .podspec 文件
    while IFS= read -r podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 跳过备份和应用目录（使用现有的验证函数）
        if command -v is_valid_pod_directory >/dev/null 2>&1; then
            if ! is_valid_pod_directory "$name"; then
                continue
            fi
        else
            # 基础过滤逻辑 - 使用通用的过滤规则
            if [[ "$name" == backup_* ]] || [[ "$name" == *"App" ]] || [[ "$name" == "pod_clean" ]] || [[ "$name" == "." ]] || [[ "$name" == ".." ]]; then
                continue
            fi
        fi

        pod_dirs+=("$dir")
    done < <(find .. -maxdepth 2 -name "*.podspec" 2>/dev/null)

    printf '%s\n' "${pod_dirs[@]}"
}

# 基于数据库记录进行回归检查
check_from_database_records() {
    log_header "基于数据库记录进行回归检查"

    if [ ! -f "$DB_FILE" ]; then
        log_warning "数据库文件不存在，无法进行回归检查"
        return 1
    fi

    # 获取所有删除记录，按Pod分组
    local all_records=$(sqlite3 "$DB_FILE" "SELECT pod_name, file_path, status, deletion_time FROM deletion_progress ORDER BY pod_name, deletion_time DESC;" 2>/dev/null)

    if [ -z "$all_records" ]; then
        log_info "数据库中没有删除记录"
        return 0
    fi

    # 统计信息
    local total_records=0
    local processed_records=0
    local current_pod=""
    local pod_records=()

    # 计算总记录数
    total_records=$(echo "$all_records" | wc -l)
    log_info "数据库中共有 $total_records 条删除记录"

    # 按Pod处理记录
    while IFS='|' read -r pod_name file_path status deletion_time; do
        if [ -z "$pod_name" ] || [ -z "$file_path" ]; then
            continue
        fi

        # 如果指定了特定Pod，只处理该Pod
        if [ -n "$TARGET_POD" ] && [ "$pod_name" != "$TARGET_POD" ]; then
            continue
        fi

        # 当切换到新Pod时，处理之前Pod的记录
        if [ "$current_pod" != "$pod_name" ]; then
            if [ -n "$current_pod" ] && [ ${#pod_records[@]} -gt 0 ]; then
                check_pod_deletion_records "$current_pod" "${pod_records[@]}"
            fi

            current_pod="$pod_name"
            pod_records=()
            log_header "检查Pod: $pod_name 的删除记录"
        fi

        pod_records+=("$file_path|$status|$deletion_time")
        processed_records=$((processed_records + 1))

    done <<< "$all_records"

    # 处理最后一个Pod的记录
    if [ -n "$current_pod" ] && [ ${#pod_records[@]} -gt 0 ]; then
        check_pod_deletion_records "$current_pod" "${pod_records[@]}"
    fi

    log_success "回归检查完成，共处理 $processed_records 条记录"
}

# 并行检查单个文件记录
check_single_file_record() {
    local pod_name="$1"
    local pod_dir="$2"
    local record="$3"
    local temp_dir="$4"
    local file_index="$5"

    IFS='|' read -r file_path status deletion_time <<< "$record"

    # 创建临时结果文件
    local result_file="$temp_dir/result_${file_index}.txt"
    local status_file="$temp_dir/status_${file_index}.txt"
    local search_log_file="$temp_dir/search_log_${file_index}.txt"

    # 切换到Pod目录
    if ! cd "$pod_dir" 2>/dev/null; then
        echo "ERROR|无法进入目录: $pod_dir" > "$result_file"
        echo "error" > "$status_file"
        return 1
    fi

    # 重定向回归检查的输出到搜索日志文件
    {
        echo "🔍 回归检查已删除文件: $(basename "$file_path")"
        echo "📝 开始模糊搜索引用检查..."
    } > "$search_log_file"

    # 对已删除文件进行回归引用检查
    local references=()
    while IFS= read -r ref; do
        references+=("$ref")
    done < <(check_file_references_regression "$file_path" "$pod_dir" 2>&1 | tee -a "$search_log_file")

    # 记录检查结果
    echo "DELETED|$file_path|$status|$deletion_time" > "$result_file"

    if [ ${#references[@]} -gt 0 ]; then
        echo "risky" > "$status_file"
        # 保存引用信息
        printf '%s\n' "${references[@]}" > "$temp_dir/refs_${file_index}.txt"
        {
            echo "⚠️ 已删除文件发现 ${#references[@]} 个潜在引用，可能误删"
            echo "具体引用信息："
            for ref in "${references[@]}"; do
                echo "  • $ref"
            done
        } >> "$search_log_file"
    else
        echo "safe" > "$status_file"
        echo "✅ 已删除文件未发现引用，删除安全" >> "$search_log_file"
    fi
}

# 并行检查Pod的删除记录
check_pod_deletion_records_parallel() {
    local pod_name="$1"
    shift
    local records=("$@")

    # 查找Pod目录
    local pod_dir=""
    while IFS= read -r dir; do
        if [ "$(basename "$dir")" = "$pod_name" ]; then
            pod_dir="$dir"
            break
        fi
    done < <(discover_pod_projects)

    if [ -z "$pod_dir" ]; then
        log_error "未找到Pod目录: $pod_name"
        return 1
    fi

    log_info "Pod目录: $pod_dir"
    log_info "删除记录数: ${#records[@]}"
    log_info "并行任务数: $MAX_PARALLEL_JOBS"

    # 初始化日志系统
    init_logging "$pod_name"

    local total_files=${#records[@]}
    local safe_deletions=0
    local risky_deletions=0
    local missing_files=0
    local deleted_files=0

    # 创建临时目录和输入文件
    local temp_dir="/tmp/pod_checker_$$"
    mkdir -p "$temp_dir"
    local input_file="$temp_dir/input.txt"

    # 将记录写入临时文件，每行一个记录
    for i in "${!records[@]}"; do
        echo "$((i + 1))|${records[i]}" >> "$input_file"
    done

    log_info "开始并行检查..."

    # 使用GNU parallel或简单的后台任务进行并行处理
    if command -v parallel >/dev/null 2>&1; then
        # 使用GNU parallel
        cat "$input_file" | parallel -j "$MAX_PARALLEL_JOBS" --colsep '|' \
        "check_single_file_record '$pod_name' '$pod_dir' '{2}' '$temp_dir' '{1}'"
    else
        # 使用简单的后台任务方式
        local job_count=0
        while IFS='|' read -r index record; do
            # 启动后台任务
            (check_single_file_record "$pod_name" "$pod_dir" "$record" "$temp_dir" "$index") &

            job_count=$((job_count + 1))

            # 控制并发数量
            if [ $((job_count % MAX_PARALLEL_JOBS)) -eq 0 ]; then
                wait  # 等待当前批次完成
            fi
        done < "$input_file"

        # 等待所有剩余任务完成
        wait
    fi

    log_info "并行检查完成，正在汇总结果..."

    # 汇总结果
    local processed=0
    for i in $(seq 1 $total_files); do
        local result_file="$temp_dir/result_${i}.txt"
        local status_file="$temp_dir/status_${i}.txt"
        local refs_file="$temp_dir/refs_${i}.txt"

        if [ -f "$result_file" ] && [ -f "$status_file" ]; then
            processed=$((processed + 1))
            local result_line=$(cat "$result_file")
            local file_status=$(cat "$status_file")

            IFS='|' read -r result_type file_path status deletion_time <<< "$result_line"

            echo ""
            echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
            echo "进度: $processed/$total_files"
            echo "📋 删除记录: $file_path"
            echo "📅 删除时间: $deletion_time"
            echo "📊 状态: $status"

            # 显示回归检查的详细过程
            echo "🔍 开始回归检查（误删检测）..."
            local search_log_file="$temp_dir/search_log_${i}.txt"
            if [ -f "$search_log_file" ]; then
                cat "$search_log_file"
            fi

            case "$result_type" in
                "DELETED")
                    deleted_files=$((deleted_files + 1))
                    if [ "$status" != "completed" ]; then
                        log_info "更新删除状态为已完成"
                        mark_deletion_completed "$pod_name" "$file_path"
                    fi

                    case "$file_status" in
                        "risky")
                            if [ -f "$refs_file" ]; then
                                local ref_count=$(wc -l < "$refs_file")
                                echo "⚠️  误删风险：已删除文件发现 $ref_count 个潜在引用"
                                echo "具体引用信息："
                                while IFS= read -r ref; do
                                    echo "   • $ref"
                                done < "$refs_file"
                            else
                                echo "⚠️  误删风险：已删除文件仍有潜在引用"
                            fi
                            risky_deletions=$((risky_deletions + 1))
                            ;;
                        "safe")
                            echo "✅ 删除安全：未发现引用，删除合理"
                            safe_deletions=$((safe_deletions + 1))
                            ;;
                    esac
                    ;;
                "ERROR")
                    log_error "$file_path"
                    ;;
            esac
        fi
    done

    # 清理临时目录
    rm -rf "$temp_dir"

    # 显示总结
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_header "Pod $pod_name 误删检测完成"
    echo "总计检查记录: $total_files 条"
    echo "已处理文件: $deleted_files 个"
    echo "安全删除: $safe_deletions 个"
    echo "疑似误删: $risky_deletions 个"

    # 记录总结到日志
    if [ -n "$CURRENT_LOG_FILE" ]; then
        {
            echo ""
            echo "=================================="
            echo "误删检测总结"
            echo "=================================="
            echo "总计检查记录: $total_files 条"
            echo "已处理文件: $deleted_files 个"
            echo "安全删除: $safe_deletions 个"
            echo "疑似误删: $risky_deletions 个"
            echo "检查完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
        } >> "$CURRENT_LOG_FILE"

        echo ""
        log_success "详细回归检查日志已保存到: $CURRENT_LOG_FILE"
    fi
}

# 检查单个Pod的删除记录
check_pod_deletion_records() {
    local pod_name="$1"
    shift
    local records=("$@")

    # 根据并行模式选择处理方式
    if [ "$PARALLEL_MODE" = "true" ] && [ "$AUTO_MODE" = "true" ]; then
        check_pod_deletion_records_parallel "$pod_name" "${records[@]}"
        return
    fi

    # 查找Pod目录
    local pod_dir=""
    while IFS= read -r dir; do
        if [ "$(basename "$dir")" = "$pod_name" ]; then
            pod_dir="$dir"
            break
        fi
    done < <(discover_pod_projects)

    if [ -z "$pod_dir" ]; then
        log_error "未找到Pod目录: $pod_name"
        return 1
    fi

    log_info "Pod目录: $pod_dir"
    log_info "删除记录数: ${#records[@]}"

    # 初始化日志系统
    init_logging "$pod_name"

    # 切换到Pod目录
    if ! cd "$pod_dir" 2>/dev/null; then
        log_error "无法进入目录: $pod_dir"
        return 1
    fi

    local total_files=${#records[@]}
    local processed=0
    local safe_deletions=0
    local risky_deletions=0
    local missing_files=0

    # 逐个检查删除记录
    for record in "${records[@]}"; do
        IFS='|' read -r file_path status deletion_time <<< "$record"
        processed=$((processed + 1))

        echo ""
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "进度: $processed/$total_files"
        echo "📋 删除记录: $file_path"
        echo "📅 删除时间: $deletion_time"
        echo "📊 状态: $status"

        # 检查文件是否仍然存在
        if [ -f "$file_path" ]; then
            log_warning "文件仍然存在，可能删除未完成"
            missing_files=$((missing_files + 1))

            echo "🔍 开始回归检查（兜底模糊搜索）..."

            # 使用回归检查（更宽松的兜底检查）
            local references=()
            while IFS= read -r ref; do
                references+=("$ref")
            done < <(check_file_references_regression "$file_path" "$pod_dir")

            local has_references="false"
            if [ ${#references[@]} -gt 0 ]; then
                has_references="true"
                risky_deletions=$((risky_deletions + 1))
                echo "⚠️  回归检查发现 ${#references[@]} 个潜在引用"
            else
                safe_deletions=$((safe_deletions + 1))
                echo "✅ 回归检查未发现引用，确认可安全删除"
            fi

            # 显示引用详情
            show_reference_details "$file_path" "$pod_dir" "${references[@]}"

            # 交互式处理（如果不是自动模式）
            if [ "$AUTO_MODE" = "false" ]; then
                local action=""
                local confirm_result
                interactive_confirm_deletion "$file_path" "$pod_dir" "$has_references"
                confirm_result=$?

                case $confirm_result in
                    0)
                        action="delete"
                        echo "🗑️  用户确认删除: $file_path"
                        mark_deletion_completed "$pod_name" "$file_path"
                        ;;
                    1)
                        action="skip"
                        echo "⏭️  用户跳过文件: $file_path"
                        ;;
                    2)
                        action="restore"
                        echo "🔄 用户选择恢复文件: $file_path"
                        ;;
                    4)
                        log_info "用户选择退出检查"
                        return 0
                        ;;
                esac
            else
                if [ "$has_references" = "false" ]; then
                    echo "🤖 自动模式: 建议删除无引用文件"
                    safe_deletions=$((safe_deletions + 1))
                else
                    echo "🤖 自动模式: 跳过有引用文件"
                    risky_deletions=$((risky_deletions + 1))
                fi
            fi
        else
            echo "✅ 文件已删除，记录状态正确"
            if [ "$status" != "completed" ]; then
                log_info "更新删除状态为已完成"
                mark_deletion_completed "$pod_name" "$file_path"
            fi
        fi

        # 在非自动模式下，给用户一点时间查看结果
        if [ "$AUTO_MODE" = "false" ]; then
            sleep 0.5
        fi
    done

    # 显示总结
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_header "Pod $pod_name 回归检查完成"
    echo "总计记录: $total_files 条"
    echo "安全删除: $safe_deletions 个"
    echo "风险删除: $risky_deletions 个"
    echo "文件缺失: $missing_files 个"

    # 记录总结到日志
    if [ -n "$CURRENT_LOG_FILE" ]; then
        {
            echo ""
            echo "=================================="
            echo "回归检查总结"
            echo "=================================="
            echo "总计记录: $total_files 条"
            echo "安全删除: $safe_deletions 个"
            echo "风险删除: $risky_deletions 个"
            echo "文件缺失: $missing_files 个"
            echo "检查完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
        } >> "$CURRENT_LOG_FILE"

        echo ""
        log_success "详细回归检查日志已保存到: $CURRENT_LOG_FILE"
    fi
}

# 检查数据库记录与Git状态的一致性
check_db_consistency() {
    log_header "检查数据库记录与Git状态的一致性"

    if [ ! -f "$DB_FILE" ]; then
        log_warning "数据库文件不存在"
        return 1
    fi

    # 发现所有Pod项目
    local pod_projects=()
    while IFS= read -r pod_dir; do
        pod_projects+=("$pod_dir")
    done < <(discover_pod_projects)

    local total_inconsistencies=0

    for pod_dir in "${pod_projects[@]}"; do
        local pod_name=$(basename "$pod_dir")
        log_info "检查Pod: $pod_name"

        # 切换到Pod目录
        if ! cd "$pod_dir" 2>/dev/null; then
            log_error "无法进入目录: $pod_dir"
            continue
        fi

        # 获取Git中被删除的文件
        local git_deleted=()
        while IFS= read -r img; do
            git_deleted+=("$img")
        done < <(get_deleted_images "$pod_dir")

        # 获取数据库中的删除记录
        local db_records=$(get_deleted_records_from_db "$pod_name")

        # 检查一致性
        local inconsistencies=0

        # 检查Git删除但数据库中没有记录的文件
        for git_file in "${git_deleted[@]}"; do
            if ! is_file_in_deletion_records "$pod_name" "$git_file"; then
                log_warning "Git中已删除但数据库中无记录: $git_file"
                inconsistencies=$((inconsistencies + 1))
            fi
        done

        # 检查数据库中有记录但Git中未删除的文件
        while IFS='|' read -r file_path status deletion_time; do
            if [ -n "$file_path" ] && [ -f "$file_path" ]; then
                log_warning "数据库中有删除记录但文件仍存在: $file_path (状态: $status)"
                inconsistencies=$((inconsistencies + 1))
            fi
        done <<< "$db_records"

        if [ $inconsistencies -eq 0 ]; then
            log_success "Pod $pod_name: 数据库与Git状态一致"
        else
            log_error "Pod $pod_name: 发现 $inconsistencies 个不一致项"
            total_inconsistencies=$((total_inconsistencies + inconsistencies))
        fi
    done

    if [ $total_inconsistencies -eq 0 ]; then
        log_success "所有Pod的数据库记录与Git状态一致"
    else
        log_error "总计发现 $total_inconsistencies 个不一致项"
    fi
}

# 显示主菜单
show_main_menu() {
    echo ""
    echo "=== 图片资源删除安全检查工具菜单 ==="
    echo "1. 基于数据库记录的并行回归检查 (推荐)"
    echo "2. 传统的Pod项目遍历检查"
    echo "3. 显示删除记录摘要"
    echo "4. 检查数据库与Git状态一致性"
    echo "5. 检查指定Pod项目"
    echo "6. 查看回归检查日志"
    echo "7. 显示帮助信息"
    echo "8. 退出"
    echo ""
    echo -n "请选择功能 (1-8): "
}

# 交互式主菜单
interactive_main_menu() {
    while true; do
        show_main_menu
        local choice
        read -r choice

        case "$choice" in
            1)
                echo ""
                log_info "启动基于数据库记录的并行回归检查..."
                echo -n "请输入并行任务数量 (默认: $MAX_PARALLEL_JOBS): "
                local jobs_input
                read -r jobs_input
                if [ -n "$jobs_input" ] && [[ "$jobs_input" =~ ^[0-9]+$ ]] && [ "$jobs_input" -gt 0 ]; then
                    MAX_PARALLEL_JOBS="$jobs_input"
                fi
                log_info "使用 $MAX_PARALLEL_JOBS 个并行任务"
                DB_FIRST_MODE=true
                PARALLEL_MODE=true
                break
                ;;
            2)
                echo ""
                log_info "启动传统的Pod项目遍历检查..."
                DB_FIRST_MODE=false
                PARALLEL_MODE=false
                break
                ;;
            3)
                echo ""
                show_deletion_records_summary
                echo ""
                echo -n "按任意键继续..."
                read -n 1 -s
                ;;
            4)
                echo ""
                check_db_consistency
                echo ""
                echo -n "按任意键继续..."
                read -n 1 -s
                ;;
            5)
                echo ""
                echo -n "请输入Pod名称: "
                read -r TARGET_POD
                if [ -n "$TARGET_POD" ]; then
                    log_info "检查指定Pod: $TARGET_POD"
                    DB_FIRST_MODE=true
                    PARALLEL_MODE=true
                    break
                else
                    log_error "Pod名称不能为空"
                fi
                ;;
            6)
                echo ""
                echo -n "请输入Pod名称 (留空显示所有日志): "
                read -r log_pod_name
                show_regression_logs "$log_pod_name"
                echo ""
                echo -n "按任意键继续..."
                read -n 1 -s
                ;;
            7)
                echo ""
                show_help
                echo ""
                echo -n "按任意键继续..."
                read -n 1 -s
                ;;
            8)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
    done
}

# 主函数
main() {
    # 显示横幅
    echo -e "\033[0;36m"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              图片资源删除安全检查脚本 v2.0                     ║"
    echo "║                                                              ║"
    echo "║  功能: 检查 Git 中被删除的图片文件，防止误删重要资源            ║"
    echo "║  特性: 数据库记录检查、多种引用检查、交互式确认、批量处理        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "\033[0m"

    # 初始化数据库
    init_database

    # 解析命令行参数
    local has_args=false
    while [[ $# -gt 0 ]]; do
        has_args=true
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -p|--pod)
                TARGET_POD="$2"
                shift 2
                ;;
            -a|--auto)
                AUTO_MODE=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -r|--records)
                show_deletion_records_summary
                exit 0
                ;;
            --check-db)
                check_db_consistency
                exit 0
                ;;
            --db-first)
                DB_FIRST_MODE=true
                shift
                ;;
            --parallel)
                PARALLEL_MODE=true
                shift
                ;;
            -j|--jobs)
                if [ -n "${2:-}" ] && [[ "${2:-}" =~ ^[0-9]+$ ]] && [ "${2:-}" -gt 0 ]; then
                    MAX_PARALLEL_JOBS="$2"
                    shift 2
                else
                    log_error "-j/--jobs 需要一个正整数参数"
                    exit 1
                fi
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 如果没有命令行参数，显示交互式菜单
    if [ "$has_args" = "false" ]; then
        interactive_main_menu
    fi

    # 并行模式需要自动模式
    if [ "$PARALLEL_MODE" = "true" ] && [ "$AUTO_MODE" = "false" ]; then
        log_warning "并行模式需要自动模式，已自动启用自动模式"
        AUTO_MODE=true
    fi

    # 检查并行处理工具
    if [ "$PARALLEL_MODE" = "true" ]; then
        if ! command -v parallel >/dev/null 2>&1 && ! command -v xargs >/dev/null 2>&1; then
            log_error "并行模式需要 GNU parallel 或 xargs 工具"
            log_info "请安装 GNU parallel: brew install parallel (macOS) 或 apt-get install parallel (Ubuntu)"
            exit 1
        fi

        if command -v parallel >/dev/null 2>&1; then
            log_info "使用 GNU parallel 进行并行处理"
        else
            log_info "使用 xargs 进行并行处理"
        fi
    fi

    # 检查依赖
    local missing_deps=()
    if ! command -v git >/dev/null 2>&1; then
        missing_deps+=("git")
    fi

    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少必要依赖: ${missing_deps[*]}"
        exit 1
    fi

    # 检查是否在正确的目录结构中
    if [ ! -d "../" ]; then
        log_error "请在包含 Pod 项目的目录中运行此脚本"
        exit 1
    fi

    # 发现 Pod 项目
    local pod_projects=()
    while IFS= read -r pod_dir; do
        pod_projects+=("$pod_dir")
    done < <(discover_pod_projects)

    if [ ${#pod_projects[@]} -eq 0 ]; then
        log_warning "未发现任何 Pod 项目"
        exit 0
    fi

    log_info "发现 ${#pod_projects[@]} 个 Pod 项目"

    # 根据模式选择检查方式
    if [ "$DB_FIRST_MODE" = "true" ]; then
        # 优先从数据库记录进行回归检查
        check_from_database_records
    else
        # 传统的Pod项目遍历检查
        if [ -n "$TARGET_POD" ]; then
            # 查找指定的 Pod
            local found_pod=""
            for pod_dir in "${pod_projects[@]}"; do
                local pod_name=$(basename "$pod_dir")
                if [ "$pod_name" = "$TARGET_POD" ]; then
                    found_pod="$pod_dir"
                    break
                fi
            done

            if [ -z "$found_pod" ]; then
                log_error "未找到指定的 Pod: $TARGET_POD"
                echo "可用的 Pod 项目:"
                for pod_dir in "${pod_projects[@]}"; do
                    echo "  - $(basename "$pod_dir")"
                done
                exit 1
            fi

            check_pod_project "$found_pod"
        else
            # 检查所有 Pod 项目
            local total_pods=${#pod_projects[@]}
            local current_pod=0

            for pod_dir in "${pod_projects[@]}"; do
                current_pod=$((current_pod + 1))
                echo ""
                echo "═══════════════════════════════════════════════════════════════════════════════"
                echo "Pod 项目 $current_pod/$total_pods"

                check_pod_project "$pod_dir"

                # 在非自动模式下，询问是否继续
                if [ "$AUTO_MODE" = "false" ] && [ $current_pod -lt $total_pods ]; then
                    echo ""
                    echo -n "继续检查下一个 Pod 项目? (y/n/q/s): "
                    echo "  y - 继续下一个"
                    echo "  n - 跳过剩余项目"
                    echo "  q - 退出检查"
                    echo "  s - 跳过到指定Pod"
                    echo -n "请选择: "
                    local continue_choice
                    read -r continue_choice

                    case "$continue_choice" in
                        n|N)
                            log_info "跳过剩余 Pod 项目"
                            break
                            ;;
                        q|Q)
                            log_info "退出检查"
                            exit 0
                            ;;
                        s|S)
                            echo -n "请输入要跳转到的Pod名称: "
                            local target_pod_name
                            read -r target_pod_name

                            # 查找目标Pod
                            local found_target=false
                            for remaining_pod in "${pod_projects[@]:$current_pod}"; do
                                local remaining_pod_name=$(basename "$remaining_pod")
                                if [[ "$remaining_pod_name" == *"$target_pod_name"* ]]; then
                                    log_info "跳转到Pod: $remaining_pod_name"
                                    check_pod_project "$remaining_pod"
                                    found_target=true
                                    break
                                fi
                            done

                            if [ "$found_target" = "false" ]; then
                                log_warning "未找到匹配的Pod: $target_pod_name"
                            fi
                            ;;
                        *)
                            # 默认继续
                            ;;
                    esac
                fi
            done
        fi
    fi

    echo ""
    if [ "$DB_FIRST_MODE" = "true" ]; then
        if [ "$PARALLEL_MODE" = "true" ]; then
            log_success "基于数据库记录的并行回归检查完成"
            echo ""
            echo "💡 提示:"
            echo "   - 已完成对数据库删除记录的并行回归检查"
            echo "   - 并行任务数: $MAX_PARALLEL_JOBS"
            echo "   - 建议定期运行此检查以确保删除操作的一致性"
            echo "   - 使用 --check-db 选项检查数据库与Git状态的一致性"
        else
            log_success "基于数据库记录的回归检查完成"
            echo ""
            echo "💡 提示:"
            echo "   - 已完成对数据库删除记录的回归检查"
            echo "   - 建议定期运行此检查以确保删除操作的一致性"
            echo "   - 使用 --check-db 选项检查数据库与Git状态的一致性"
            echo "   - 使用 --parallel 选项启用并行处理以提高速度"
        fi
    else
        log_success "图片删除安全检查完成"
        echo ""
        echo "💡 提示:"
        echo "   - 此脚本仅进行安全检查，不会自动删除文件"
        echo "   - 要完成删除操作，请手动执行相应的 git 命令"
        echo "   - 建议在删除前创建备份或使用 git stash"
        echo "   - 如有疑问，可以使用 git checkout 恢复文件"
        echo "   - 使用 --db-first 选项进行基于数据库记录的回归检查"
        echo "   - 使用 --parallel 选项启用并行处理以提高速度"
    fi
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
