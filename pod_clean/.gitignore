# Pod库优化工具 - Git忽略文件

# 备份目录
backup_*/
resource_backup_*/
unused_resources_backup_*/
pods_optimization_backup_*/

# SQLite 数据库文件（运行时生成）
# 仅版本化迁移脚本与种子数据，不提交运行期的SQLite二进制文件
db/*.sqlite
db/*.db
db/*.sqlite3
db/*.sqlite-wal
db/*.sqlite-shm

# 保留同步目录（CSV格式的可版本化数据）
!db/sync/
!db/sync/*.csv

# 报告目录（自动生成）

# 临时文件
*.tmp
*.temp
.DS_Store

# 日志文件
*.log
optimization_report_*.txt

# 压缩工具临时文件
*.pngquant-*
*.jpegoptim-*

# 系统文件
Thumbs.db
.Spotlight-V100
.Trashes
*.db
mac/pod_clean.xcodeproj/project.xcworkspace/xcuserdata
